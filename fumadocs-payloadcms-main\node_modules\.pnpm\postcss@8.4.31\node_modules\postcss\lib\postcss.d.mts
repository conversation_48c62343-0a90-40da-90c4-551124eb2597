export {
  // postcss function / namespace
  default,

  // Value exports from postcss.mjs
  stringify,
  fromJSON,
  // @ts-expect-error This value exists, but it’s untyped.
  plugin,
  parse,
  list,

  document,
  comment,
  atRule,
  rule,
  decl,
  root,

  CssSyntaxError,
  Declaration,
  Container,
  Processor,
  Document,
  Comment,
  Warning,
  AtRule,
  Result,
  Input,
  Rule,
  Root,
  Node,

  // Type-only exports
  AcceptedPlugin,
  AnyNode,
  AtRuleProps,
  Builder,
  ChildNode,
  ChildProps,
  CommentProps,
  ContainerProps,
  DeclarationProps,
  DocumentProps,
  FilePosition,
  Helpers,
  JSONHydrator,
  Message,
  NodeErrorOptions,
  NodeProps,
  OldPlugin,
  Parser,
  Plugin,
  PluginCreator,
  Position,
  Postcss,
  ProcessOptions,
  RootProps,
  RuleProps,
  Source,
  SourceMap,
  SourceMapOptions,
  Stringifier,
  Syntax,
  TransformCallback,
  Transformer,
  WarningOptions,

  // This is a class, but it’s not re-exported. That’s why it’s exported as type-only here.
  type LazyResult,

} from './postcss.js'
