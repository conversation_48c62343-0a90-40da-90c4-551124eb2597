{"version": "6", "dialect": "sqlite", "tables": {"users_roles": {"name": "users_roles", "columns": {"order": {"name": "order", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "parent_id": {"name": "parent_id", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "value": {"name": "value", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": false}}, "indexes": {"users_roles_order_idx": {"name": "users_roles_order_idx", "columns": ["order"], "isUnique": false}, "users_roles_parent_idx": {"name": "users_roles_parent_idx", "columns": ["parent_id"], "isUnique": false}}, "foreignKeys": {"users_roles_parent_fk": {"name": "users_roles_parent_fk", "tableFrom": "users_roles", "tableTo": "users", "columnsFrom": ["parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "users_sessions": {"name": "users_sessions", "columns": {"_order": {"name": "_order", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "_parent_id": {"name": "_parent_id", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "created_at": {"name": "created_at", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "expires_at": {"name": "expires_at", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {"users_sessions_order_idx": {"name": "users_sessions_order_idx", "columns": ["_order"], "isUnique": false}, "users_sessions_parent_id_idx": {"name": "users_sessions_parent_id_idx", "columns": ["_parent_id"], "isUnique": false}}, "foreignKeys": {"users_sessions_parent_id_fk": {"name": "users_sessions_parent_id_fk", "tableFrom": "users_sessions", "tableTo": "users", "columnsFrom": ["_parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "users": {"name": "users", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": false}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "avatar_id": {"name": "avatar_id", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "updated_at": {"name": "updated_at", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(strftime('%Y-%m-%dT%H:%M:%fZ', 'now'))"}, "created_at": {"name": "created_at", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(strftime('%Y-%m-%dT%H:%M:%fZ', 'now'))"}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "reset_password_token": {"name": "reset_password_token", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "reset_password_expiration": {"name": "reset_password_expiration", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "salt": {"name": "salt", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "hash": {"name": "hash", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "login_attempts": {"name": "login_attempts", "type": "numeric", "primaryKey": false, "notNull": false, "autoincrement": false, "default": 0}, "lock_until": {"name": "lock_until", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {"users_avatar_idx": {"name": "users_avatar_idx", "columns": ["avatar_id"], "isUnique": false}, "users_updated_at_idx": {"name": "users_updated_at_idx", "columns": ["updated_at"], "isUnique": false}, "users_created_at_idx": {"name": "users_created_at_idx", "columns": ["created_at"], "isUnique": false}, "users_email_idx": {"name": "users_email_idx", "columns": ["email"], "isUnique": true}}, "foreignKeys": {"users_avatar_id_media_id_fk": {"name": "users_avatar_id_media_id_fk", "tableFrom": "users", "tableTo": "media", "columnsFrom": ["avatar_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "media": {"name": "media", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": false}, "alt": {"name": "alt", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "prefix": {"name": "prefix", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "'media'"}, "updated_at": {"name": "updated_at", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(strftime('%Y-%m-%dT%H:%M:%fZ', 'now'))"}, "created_at": {"name": "created_at", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(strftime('%Y-%m-%dT%H:%M:%fZ', 'now'))"}, "url": {"name": "url", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "thumbnail_u_r_l": {"name": "thumbnail_u_r_l", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "filename": {"name": "filename", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "mime_type": {"name": "mime_type", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "filesize": {"name": "filesize", "type": "numeric", "primaryKey": false, "notNull": false, "autoincrement": false}, "width": {"name": "width", "type": "numeric", "primaryKey": false, "notNull": false, "autoincrement": false}, "height": {"name": "height", "type": "numeric", "primaryKey": false, "notNull": false, "autoincrement": false}, "focal_x": {"name": "focal_x", "type": "numeric", "primaryKey": false, "notNull": false, "autoincrement": false}, "focal_y": {"name": "focal_y", "type": "numeric", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {"media_updated_at_idx": {"name": "media_updated_at_idx", "columns": ["updated_at"], "isUnique": false}, "media_created_at_idx": {"name": "media_created_at_idx", "columns": ["created_at"], "isUnique": false}, "media_filename_idx": {"name": "media_filename_idx", "columns": ["filename"], "isUnique": true}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "payload_locked_documents": {"name": "payload_locked_documents", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": false}, "global_slug": {"name": "global_slug", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "updated_at": {"name": "updated_at", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(strftime('%Y-%m-%dT%H:%M:%fZ', 'now'))"}, "created_at": {"name": "created_at", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(strftime('%Y-%m-%dT%H:%M:%fZ', 'now'))"}}, "indexes": {"payload_locked_documents_global_slug_idx": {"name": "payload_locked_documents_global_slug_idx", "columns": ["global_slug"], "isUnique": false}, "payload_locked_documents_updated_at_idx": {"name": "payload_locked_documents_updated_at_idx", "columns": ["updated_at"], "isUnique": false}, "payload_locked_documents_created_at_idx": {"name": "payload_locked_documents_created_at_idx", "columns": ["created_at"], "isUnique": false}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "payload_locked_documents_rels": {"name": "payload_locked_documents_rels", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": false}, "order": {"name": "order", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "parent_id": {"name": "parent_id", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "path": {"name": "path", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "users_id": {"name": "users_id", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "media_id": {"name": "media_id", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {"payload_locked_documents_rels_order_idx": {"name": "payload_locked_documents_rels_order_idx", "columns": ["order"], "isUnique": false}, "payload_locked_documents_rels_parent_idx": {"name": "payload_locked_documents_rels_parent_idx", "columns": ["parent_id"], "isUnique": false}, "payload_locked_documents_rels_path_idx": {"name": "payload_locked_documents_rels_path_idx", "columns": ["path"], "isUnique": false}, "payload_locked_documents_rels_users_id_idx": {"name": "payload_locked_documents_rels_users_id_idx", "columns": ["users_id"], "isUnique": false}, "payload_locked_documents_rels_media_id_idx": {"name": "payload_locked_documents_rels_media_id_idx", "columns": ["media_id"], "isUnique": false}}, "foreignKeys": {"payload_locked_documents_rels_parent_fk": {"name": "payload_locked_documents_rels_parent_fk", "tableFrom": "payload_locked_documents_rels", "tableTo": "payload_locked_documents", "columnsFrom": ["parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "payload_locked_documents_rels_users_fk": {"name": "payload_locked_documents_rels_users_fk", "tableFrom": "payload_locked_documents_rels", "tableTo": "users", "columnsFrom": ["users_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "payload_locked_documents_rels_media_fk": {"name": "payload_locked_documents_rels_media_fk", "tableFrom": "payload_locked_documents_rels", "tableTo": "media", "columnsFrom": ["media_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "payload_preferences": {"name": "payload_preferences", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": false}, "key": {"name": "key", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "value": {"name": "value", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "updated_at": {"name": "updated_at", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(strftime('%Y-%m-%dT%H:%M:%fZ', 'now'))"}, "created_at": {"name": "created_at", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(strftime('%Y-%m-%dT%H:%M:%fZ', 'now'))"}}, "indexes": {"payload_preferences_key_idx": {"name": "payload_preferences_key_idx", "columns": ["key"], "isUnique": false}, "payload_preferences_updated_at_idx": {"name": "payload_preferences_updated_at_idx", "columns": ["updated_at"], "isUnique": false}, "payload_preferences_created_at_idx": {"name": "payload_preferences_created_at_idx", "columns": ["created_at"], "isUnique": false}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "payload_preferences_rels": {"name": "payload_preferences_rels", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": false}, "order": {"name": "order", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "parent_id": {"name": "parent_id", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "path": {"name": "path", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "users_id": {"name": "users_id", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {"payload_preferences_rels_order_idx": {"name": "payload_preferences_rels_order_idx", "columns": ["order"], "isUnique": false}, "payload_preferences_rels_parent_idx": {"name": "payload_preferences_rels_parent_idx", "columns": ["parent_id"], "isUnique": false}, "payload_preferences_rels_path_idx": {"name": "payload_preferences_rels_path_idx", "columns": ["path"], "isUnique": false}, "payload_preferences_rels_users_id_idx": {"name": "payload_preferences_rels_users_id_idx", "columns": ["users_id"], "isUnique": false}}, "foreignKeys": {"payload_preferences_rels_parent_fk": {"name": "payload_preferences_rels_parent_fk", "tableFrom": "payload_preferences_rels", "tableTo": "payload_preferences", "columnsFrom": ["parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "payload_preferences_rels_users_fk": {"name": "payload_preferences_rels_users_fk", "tableFrom": "payload_preferences_rels", "tableTo": "users", "columnsFrom": ["users_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "payload_migrations": {"name": "payload_migrations", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": false}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "batch": {"name": "batch", "type": "numeric", "primaryKey": false, "notNull": false, "autoincrement": false}, "updated_at": {"name": "updated_at", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(strftime('%Y-%m-%dT%H:%M:%fZ', 'now'))"}, "created_at": {"name": "created_at", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(strftime('%Y-%m-%dT%H:%M:%fZ', 'now'))"}}, "indexes": {"payload_migrations_updated_at_idx": {"name": "payload_migrations_updated_at_idx", "columns": ["updated_at"], "isUnique": false}, "payload_migrations_created_at_idx": {"name": "payload_migrations_created_at_idx", "columns": ["created_at"], "isUnique": false}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}}, "views": {}, "enums": {}, "_meta": {"tables": {}, "columns": {}}, "internal": {"indexes": {}}, "id": "0eec4618-6578-4a2c-b43c-7eb3729aa62e", "prevId": "00000000-0000-0000-0000-000000000000"}