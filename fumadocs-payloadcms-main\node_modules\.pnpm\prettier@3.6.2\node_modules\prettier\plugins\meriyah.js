(function(f){function e(){var i=f();return i.default||i}if(typeof exports=="object"&&typeof module=="object")module.exports=e();else if(typeof define=="function"&&define.amd)define(e);else{var t=typeof globalThis<"u"?globalThis:typeof global<"u"?global:typeof self<"u"?self:this||{};t.prettierPlugins=t.prettierPlugins||{},t.prettierPlugins.meriyah=e()}})(function(){"use strict";var K2=Object.defineProperty;var Jn=Object.getOwnPropertyDescriptor;var _n=Object.getOwnPropertyNames;var jn=Object.prototype.hasOwnProperty;var Ce=(e,n)=>{for(var t in n)K2(e,t,{get:n[t],enumerable:!0})},Xn=(e,n,t,u)=>{if(n&&typeof n=="object"||typeof n=="function")for(let o of _n(n))!jn.call(e,o)&&o!==t&&K2(e,o,{get:()=>n[o],enumerable:!(u=Jn(n,o))||u.enumerable});return e};var Hn=e=>Xn(K2({},"__esModule",{value:!0}),e);var u1={};Ce(u1,{parsers:()=>De});var De={};Ce(De,{meriyah:()=>t1});var zn=(e,n,t,u)=>{if(!(e&&n==null))return n.replaceAll?n.replaceAll(t,u):t.global?n.replace(t,u):n.split(t).join(u)},G=zn;var Kn={0:"Unexpected token",30:"Unexpected token: '%0'",1:"Octal escape sequences are not allowed in strict mode",2:"Octal escape sequences are not allowed in template strings",3:"\\8 and \\9 are not allowed in template strings",4:"Private identifier #%0 is not defined",5:"Illegal Unicode escape sequence",6:"Invalid code point %0",7:"Invalid hexadecimal escape sequence",9:"Octal literals are not allowed in strict mode",8:"Decimal integer literals with a leading zero are forbidden in strict mode",10:"Expected number in radix %0",151:"Invalid left-hand side assignment to a destructible right-hand side",11:"Non-number found after exponent indicator",12:"Invalid BigIntLiteral",13:"No identifiers allowed directly after numeric literal",14:"Escapes \\8 or \\9 are not syntactically valid escapes",15:"Escapes \\8 or \\9 are not allowed in strict mode",16:"Unterminated string literal",17:"Unterminated template literal",18:"Multiline comment was not closed properly",19:"The identifier contained dynamic unicode escape that was not closed",20:"Illegal character '%0'",21:"Missing hexadecimal digits",22:"Invalid implicit octal",23:"Invalid line break in string literal",24:"Only unicode escapes are legal in identifier names",25:"Expected '%0'",26:"Invalid left-hand side in assignment",27:"Invalid left-hand side in async arrow",28:'Calls to super must be in the "constructor" method of a class expression or class declaration that has a superclass',29:"Member access on super must be in a method",31:"Await expression not allowed in formal parameter",32:"Yield expression not allowed in formal parameter",95:"Unexpected token: 'escaped keyword'",33:"Unary expressions as the left operand of an exponentiation expression must be disambiguated with parentheses",123:"Async functions can only be declared at the top level or inside a block",34:"Unterminated regular expression",35:"Unexpected regular expression flag",36:"Duplicate regular expression flag '%0'",37:"%0 functions must have exactly %1 argument%2",38:"Setter function argument must not be a rest parameter",39:"%0 declaration must have a name in this context",40:"Function name may not contain any reserved words or be eval or arguments in strict mode",41:"The rest operator is missing an argument",42:"A getter cannot be a generator",43:"A setter cannot be a generator",44:"A computed property name must be followed by a colon or paren",134:"Object literal keys that are strings or numbers must be a method or have a colon",46:"Found `* async x(){}` but this should be `async * x(){}`",45:"Getters and setters can not be generators",47:"'%0' can not be generator method",48:"No line break is allowed after '=>'",49:"The left-hand side of the arrow can only be destructed through assignment",50:"The binding declaration is not destructible",51:"Async arrow can not be followed by new expression",52:"Classes may not have a static property named 'prototype'",53:"Class constructor may not be a %0",54:"Duplicate constructor method in class",55:"Invalid increment/decrement operand",56:"Invalid use of `new` keyword on an increment/decrement expression",57:"`=>` is an invalid assignment target",58:"Rest element may not have a trailing comma",59:"Missing initializer in %0 declaration",60:"'for-%0' loop head declarations can not have an initializer",61:"Invalid left-hand side in for-%0 loop: Must have a single binding",62:"Invalid shorthand property initializer",63:"Property name __proto__ appears more than once in object literal",64:"Let is disallowed as a lexically bound name",65:"Invalid use of '%0' inside new expression",66:"Illegal 'use strict' directive in function with non-simple parameter list",67:'Identifier "let" disallowed as left-hand side expression in strict mode',68:"Illegal continue statement",69:"Illegal break statement",70:"Cannot have `let[...]` as a var name in strict mode",71:"Invalid destructuring assignment target",72:"Rest parameter may not have a default initializer",73:"The rest argument must the be last parameter",74:"Invalid rest argument",76:"In strict mode code, functions can only be declared at top level or inside a block",77:"In non-strict mode code, functions can only be declared at top level, inside a block, or as the body of an if statement",78:"Without web compatibility enabled functions can not be declared at top level, inside a block, or as the body of an if statement",79:"Class declaration can't appear in single-statement context",80:"Invalid left-hand side in for-%0",81:"Invalid assignment in for-%0",82:"for await (... of ...) is only valid in async functions and async generators",83:"The first token after the template expression should be a continuation of the template",85:"`let` declaration not allowed here and `let` cannot be a regular var name in strict mode",84:"`let \n [` is a restricted production at the start of a statement",86:"Catch clause requires exactly one parameter, not more (and no trailing comma)",87:"Catch clause parameter does not support default values",88:"Missing catch or finally after try",89:"More than one default clause in switch statement",90:"Illegal newline after throw",91:"Strict mode code may not include a with statement",92:"Illegal return statement",93:"The left hand side of the for-header binding declaration is not destructible",94:"new.target only allowed within functions or static blocks",96:"'#' not followed by identifier",102:"Invalid keyword",101:"Can not use 'let' as a class name",100:"'A lexical declaration can't define a 'let' binding",99:"Can not use `let` as variable name in strict mode",97:"'%0' may not be used as an identifier in this context",98:"Await is only valid in async functions",103:"The %0 keyword can only be used with the module goal",104:"Unicode codepoint must not be greater than 0x10FFFF",105:"%0 source must be string",106:"Only a identifier or string can be used to indicate alias",107:"Only '*' or '{...}' can be imported after default",108:"Trailing decorator may be followed by method",109:"Decorators can't be used with a constructor",110:"Can not use `await` as identifier in module or async func",111:"Can not use `await` as identifier in module",112:"HTML comments are only allowed with web compatibility (Annex B)",113:"The identifier 'let' must not be in expression position in strict mode",114:"Cannot assign to `eval` and `arguments` in strict mode",115:"The left-hand side of a for-of loop may not start with 'let'",116:"Block body arrows can not be immediately invoked without a group",117:"Block body arrows can not be immediately accessed without a group",118:"Unexpected strict mode reserved word",119:"Unexpected eval or arguments in strict mode",120:"Decorators must not be followed by a semicolon",121:"Calling delete on expression not allowed in strict mode",122:"Pattern can not have a tail",124:"Can not have a `yield` expression on the left side of a ternary",125:"An arrow function can not have a postfix update operator",126:"Invalid object literal key character after generator star",127:"Private fields can not be deleted",129:"Classes may not have a field called constructor",128:"Classes may not have a private element named constructor",130:"A class field initializer or static block may not contain arguments",131:"Generators can only be declared at the top level or inside a block",132:"Async methods are a restricted production and cannot have a newline following it",133:"Unexpected character after object literal property name",135:"Invalid key token",136:"Label '%0' has already been declared",137:"continue statement must be nested within an iteration statement",138:"Undefined label '%0'",139:"Trailing comma is disallowed inside import(...) arguments",140:"Invalid binding in JSON import",141:"import() requires exactly one argument",142:"Cannot use new with import(...)",143:"... is not allowed in import()",144:"Expected '=>'",145:"Duplicate binding '%0'",146:"Duplicate private identifier #%0",147:"Cannot export a duplicate name '%0'",150:"Duplicate %0 for-binding",148:"Exported binding '%0' needs to refer to a top-level declared variable",149:"Unexpected private field",153:"Numeric separators are not allowed at the end of numeric literals",152:"Only one underscore is allowed as numeric separator",154:"JSX value should be either an expression or a quoted JSX text",155:"Expected corresponding JSX closing tag for %0",156:"Adjacent JSX elements must be wrapped in an enclosing tag",157:"JSX attributes must only be assigned a non-empty 'expression'",158:"'%0' has already been declared",159:"'%0' shadowed a catch clause binding",160:"Dot property must be an identifier",161:"Encountered invalid input after spread/rest argument",162:"Catch without try",163:"Finally without try",164:"Expected corresponding closing tag for JSX fragment",165:"Coalescing and logical operators used together in the same expression must be disambiguated with parentheses",166:"Invalid tagged template on optional chain",167:"Invalid optional chain from super property",168:"Invalid optional chain from new expression",169:'Cannot use "import.meta" outside a module',170:"Leading decorators must be attached to a class declaration",171:"An export name cannot include a lone surrogate, found %0",172:"A string literal cannot be used as an exported binding without `from`",173:"Private fields can't be accessed on super",174:"The only valid meta property for import is 'import.meta'",175:"'import.meta' must not contain escaped characters",176:'cannot use "await" as identifier inside an async function',177:'cannot use "await" in static blocks'},i2=class extends SyntaxError{start;end;range;loc;description;constructor(n,t,u,...o){let i=Kn[u].replace(/%(\d+)/g,(c,d)=>o[d]),l="["+n.line+":"+n.column+"-"+t.line+":"+t.column+"]: "+i;super(l),this.start=n.index,this.end=t.index,this.range=[n.index,t.index],this.loc={start:{line:n.line,column:n.column},end:{line:t.line,column:t.column}},this.description=i}};function f(e,n,...t){throw new i2(e.tokenStart,e.currentLocation,n,...t)}function V2(e){throw new i2(e.start,e.end,e.type,...e.params)}function J(e,n,t,...u){throw new i2(e,n,t,...u)}function a2(e,n,t){throw new i2(e,n,t)}var Le=((e,n)=>{let t=new Uint32Array(104448),u=0,o=0;for(;u<3822;){let i=e[u++];if(i<0)o-=i;else{let l=e[u++];i&2&&(l=n[l]),i&1?t.fill(l,o,o+=e[u++]):t[o++]=l}}return t})([-1,2,26,2,27,2,5,-1,0,77595648,3,44,2,3,0,14,2,63,2,64,3,0,3,0,3168796671,0,4294956992,2,1,2,0,2,41,3,0,4,0,4294966523,3,0,4,2,16,2,65,2,0,0,4294836735,0,3221225471,0,4294901942,2,66,0,*********,3,0,2,0,4294951935,3,0,2,0,2683305983,0,2684354047,2,18,2,0,0,4294961151,3,0,2,2,19,2,0,0,608174079,2,0,2,60,2,7,2,6,0,4286611199,3,0,2,2,1,3,0,3,0,4294901711,2,40,0,4089839103,0,2961209759,0,1342439375,0,4294543342,0,3547201023,0,1577204103,0,4194240,0,4294688750,2,2,0,80831,0,4261478351,0,4294549486,2,2,0,2967484831,0,196559,0,3594373100,0,3288319768,0,8469959,2,203,2,3,0,4093640191,0,660618719,0,65487,0,4294828015,0,4092591615,0,1616920031,0,982991,2,3,2,0,0,2163244511,0,4227923919,0,4236247022,2,71,0,4284449919,0,851904,2,4,2,12,0,67076095,-1,2,72,0,1073741743,0,4093607775,-1,0,50331649,0,3265266687,2,33,0,4294844415,0,4278190047,2,20,2,137,-1,3,0,2,2,23,2,0,2,10,2,0,2,15,2,22,3,0,10,2,74,2,0,2,75,2,76,2,77,2,0,2,78,2,0,2,11,0,261632,2,25,3,0,2,2,13,2,4,3,0,18,2,79,2,5,3,0,2,2,80,0,2151677951,2,29,2,9,0,909311,3,0,2,0,814743551,2,49,0,67090432,3,0,2,2,42,2,0,2,6,2,0,2,30,2,8,0,268374015,2,110,2,51,2,0,2,81,0,134153215,-1,2,7,2,0,2,8,0,2684354559,0,67044351,0,3221160064,2,17,-1,3,0,2,2,53,0,1046528,3,0,3,2,9,2,0,2,54,0,4294960127,2,10,2,6,2,11,0,4294377472,2,12,3,0,16,2,13,2,0,2,82,2,10,2,0,2,83,2,84,2,85,2,210,2,55,0,1048577,2,86,2,14,-1,2,14,0,131042,2,87,2,88,2,89,2,0,2,34,-83,3,0,7,0,1046559,2,0,2,15,2,0,0,2147516671,2,21,3,90,2,2,0,-16,2,91,0,524222462,2,4,2,0,0,4269801471,2,4,3,0,2,2,28,2,16,3,0,2,2,17,2,0,-1,2,18,-16,3,0,206,-2,3,0,692,2,73,-1,2,18,2,10,3,0,8,2,93,2,133,2,0,0,3220242431,3,0,3,2,19,2,94,2,95,3,0,2,2,96,2,0,2,97,2,46,2,0,0,4351,2,0,2,9,3,0,2,0,67043391,0,3909091327,2,0,2,24,2,9,2,20,3,0,2,0,67076097,2,8,2,0,2,21,0,67059711,0,4236247039,3,0,2,0,939524103,0,8191999,2,101,2,102,2,22,2,23,3,0,3,0,67057663,3,0,349,2,103,2,104,2,7,-264,3,0,11,2,24,3,0,2,2,32,-1,0,3774349439,2,105,2,106,3,0,2,2,19,2,107,3,0,10,2,10,2,18,2,0,2,47,2,0,2,31,2,108,2,25,0,1638399,2,183,2,109,3,0,3,2,20,2,26,2,27,2,5,2,28,2,0,2,8,2,111,-1,2,112,2,113,2,114,-1,3,0,3,2,12,-2,2,0,2,29,-3,2,163,-4,2,20,2,0,2,36,0,1,2,0,2,67,2,6,2,12,2,10,2,0,2,115,-1,3,0,4,2,10,2,23,2,116,2,7,2,0,2,117,2,0,2,118,2,119,2,120,2,0,2,9,3,0,9,2,21,2,30,2,31,2,121,2,122,-2,2,123,2,124,2,30,2,21,2,8,-2,2,125,2,30,2,32,-2,2,0,2,39,-2,0,4277137519,0,2269118463,-1,3,20,2,-1,2,33,2,38,2,0,3,30,2,2,35,2,19,-3,3,0,2,2,34,-1,2,0,2,35,2,0,2,35,2,0,2,48,2,0,0,4294950463,2,37,-7,2,0,0,203775,2,57,2,167,2,20,2,43,2,36,2,18,2,37,2,18,2,126,2,21,3,0,2,2,38,0,2151677888,2,0,2,12,0,4294901764,2,144,2,0,2,58,2,56,0,5242879,3,0,2,0,402644511,-1,2,128,2,39,0,3,-1,2,129,2,130,2,0,0,67045375,2,40,0,4226678271,0,3766565279,0,2039759,2,132,2,41,0,1046437,0,6,3,0,2,0,3288270847,0,3,3,0,2,0,67043519,-5,2,0,0,4282384383,0,1056964609,-1,3,0,2,0,67043345,-1,2,0,2,42,2,23,2,50,2,11,2,61,2,38,-5,2,0,2,12,-3,3,0,2,0,2147484671,2,134,0,4190109695,2,52,-2,2,135,0,4244635647,0,27,2,0,2,8,2,43,2,0,2,68,2,18,2,0,2,42,-6,2,0,2,45,2,59,2,44,2,45,2,46,2,47,0,8388351,-2,2,136,0,3028287487,2,48,2,138,0,33259519,2,49,-9,2,21,0,4294836223,0,3355443199,0,134152199,-2,2,69,-2,3,0,28,2,32,-3,3,0,3,2,17,3,0,6,2,50,-81,2,18,3,0,2,2,36,3,0,33,2,25,2,30,3,0,124,2,12,3,0,18,2,38,-213,2,0,2,32,-54,3,0,17,2,42,2,8,2,23,2,0,2,8,2,23,2,51,2,0,2,21,2,52,2,139,2,25,-13,2,0,2,53,-6,3,0,2,-4,3,0,2,0,4294936575,2,0,0,4294934783,-2,0,196635,3,0,191,2,54,3,0,38,2,30,2,55,2,34,-278,2,140,3,0,9,2,141,2,142,2,56,3,0,11,2,7,-72,3,0,3,2,143,0,1677656575,-130,2,26,-16,2,0,2,24,2,38,-16,0,4161266656,0,4071,2,205,-4,2,57,-13,3,0,2,2,58,2,0,2,145,2,146,2,62,2,0,2,147,2,148,2,149,3,0,10,2,150,2,151,2,22,3,58,2,3,152,2,3,59,2,0,4294954999,2,0,-16,2,0,2,92,2,0,0,2105343,0,4160749584,2,177,-34,2,8,2,154,-6,0,4194303871,0,4294903771,2,0,2,60,2,100,-3,2,0,0,1073684479,0,17407,-9,2,18,2,17,2,0,2,32,-14,2,18,2,32,-6,2,18,2,12,-15,2,155,3,0,6,0,8323103,-1,3,0,2,2,61,-37,2,62,2,156,2,157,2,158,2,159,2,160,-105,2,26,-32,3,0,1335,-1,3,0,129,2,32,3,0,6,2,10,3,0,180,2,161,3,0,233,2,162,3,0,18,2,10,-77,3,0,16,2,10,-47,3,0,154,2,6,3,0,130,2,25,-22250,3,0,7,2,25,-6130,3,5,2,-1,0,69207040,3,44,2,3,0,14,2,63,2,64,-3,0,3168731136,0,4294956864,2,1,2,0,2,41,3,0,4,0,4294966275,3,0,4,2,16,2,65,2,0,2,34,-1,2,18,2,66,-1,2,0,0,2047,0,4294885376,3,0,2,0,3145727,0,2617294944,0,4294770688,2,25,2,67,3,0,2,0,131135,2,98,0,70256639,0,71303167,0,272,2,42,2,6,0,32511,2,0,2,49,-1,2,99,2,68,0,4278255616,0,4294836227,0,4294549473,0,600178175,0,2952806400,0,268632067,0,4294543328,0,57540095,0,1577058304,0,1835008,0,4294688736,2,70,2,69,0,33554435,2,131,2,70,2,164,0,131075,0,3594373096,0,67094296,2,69,-1,0,4294828e3,0,603979263,0,654311424,0,3,0,4294828001,0,602930687,2,171,0,393219,0,4294828016,0,671088639,0,2154840064,0,4227858435,0,4236247008,2,71,2,38,-1,2,4,0,917503,2,38,-1,2,72,0,537788335,0,4026531935,-1,0,1,-1,2,33,2,73,0,7936,-3,2,0,0,2147485695,0,1010761728,0,4292984930,0,16387,2,0,2,15,2,22,3,0,10,2,74,2,0,2,75,2,76,2,77,2,0,2,78,2,0,2,12,-1,2,25,3,0,2,2,13,2,4,3,0,18,2,79,2,5,3,0,2,2,80,0,2147745791,3,19,2,0,122879,2,0,2,9,0,276824064,-2,3,0,2,2,42,2,0,0,4294903295,2,0,2,30,2,8,-1,2,18,2,51,2,0,2,81,2,49,-1,2,21,2,0,2,29,-2,0,128,-2,2,28,2,9,0,8160,-1,2,127,0,4227907585,2,0,2,37,2,0,2,50,2,184,2,10,2,6,2,11,-1,0,74440192,3,0,6,-2,3,0,8,2,13,2,0,2,82,2,10,2,0,2,83,2,84,2,85,-3,2,86,2,14,-3,2,87,2,88,2,89,2,0,2,34,-83,3,0,7,0,817183,2,0,2,15,2,0,0,33023,2,21,3,90,2,-17,2,91,0,524157950,2,4,2,0,2,92,2,4,2,0,2,22,2,28,2,16,3,0,2,2,17,2,0,-1,2,18,-16,3,0,206,-2,3,0,692,2,73,-1,2,18,2,10,3,0,8,2,93,0,3072,2,0,0,2147516415,2,10,3,0,2,2,25,2,94,2,95,3,0,2,2,96,2,0,2,97,2,46,0,4294965179,0,7,2,0,2,9,2,95,2,9,-1,0,1761345536,2,98,0,4294901823,2,38,2,20,2,99,2,35,2,100,0,2080440287,2,0,2,34,2,153,0,3296722943,2,0,0,1046675455,0,939524101,0,1837055,2,101,2,102,2,22,2,23,3,0,3,0,7,3,0,349,2,103,2,104,2,7,-264,3,0,11,2,24,3,0,2,2,32,-1,0,2700607615,2,105,2,106,3,0,2,2,19,2,107,3,0,10,2,10,2,18,2,0,2,47,2,0,2,31,2,108,-3,2,109,3,0,3,2,20,-1,3,5,2,2,110,2,0,2,8,2,111,-1,2,112,2,113,2,114,-1,3,0,3,2,12,-2,2,0,2,29,-8,2,20,2,0,2,36,-1,2,0,2,67,2,6,2,30,2,10,2,0,2,115,-1,3,0,4,2,10,2,18,2,116,2,7,2,0,2,117,2,0,2,118,2,119,2,120,2,0,2,9,3,0,9,2,21,2,30,2,31,2,121,2,122,-2,2,123,2,124,2,30,2,21,2,8,-2,2,125,2,30,2,32,-2,2,0,2,39,-2,0,4277075969,2,30,-1,3,20,2,-1,2,33,2,126,2,0,3,30,2,2,35,2,19,-3,3,0,2,2,34,-1,2,0,2,35,2,0,2,35,2,0,2,50,2,98,0,4294934591,2,37,-7,2,0,0,197631,2,57,-1,2,20,2,43,2,37,2,18,0,3,2,18,2,126,2,21,2,127,2,54,-1,0,2490368,2,127,2,25,2,18,2,34,2,127,2,38,0,4294901904,0,4718591,2,127,2,35,0,335544350,-1,2,128,0,2147487743,0,1,-1,2,129,2,130,2,8,-1,2,131,2,70,0,3758161920,0,3,2,132,0,12582911,0,655360,-1,2,0,2,29,0,2147485568,0,3,2,0,2,25,0,176,-5,2,0,2,17,2,192,-1,2,0,2,25,2,209,-1,2,0,0,16779263,-2,2,12,-1,2,38,-5,2,0,2,133,-3,3,0,2,2,55,2,134,0,2147549183,0,2,-2,2,135,2,36,0,10,0,4294965249,0,67633151,0,4026597376,2,0,0,536871935,2,18,2,0,2,42,-6,2,0,0,1,2,59,2,17,0,1,2,46,2,25,-3,2,136,2,36,2,137,2,138,0,16778239,-10,2,35,0,4294836212,2,9,-3,2,69,-2,3,0,28,2,32,-3,3,0,3,2,17,3,0,6,2,50,-81,2,18,3,0,2,2,36,3,0,33,2,25,0,126,3,0,124,2,12,3,0,18,2,38,-213,2,10,-55,3,0,17,2,42,2,8,2,18,2,0,2,8,2,18,2,60,2,0,2,25,2,50,2,139,2,25,-13,2,0,2,73,-6,3,0,2,-4,3,0,2,0,67583,-1,2,107,-2,0,11,3,0,191,2,54,3,0,38,2,30,2,55,2,34,-278,2,140,3,0,9,2,141,2,142,2,56,3,0,11,2,7,-72,3,0,3,2,143,2,144,-187,3,0,2,2,58,2,0,2,145,2,146,2,62,2,0,2,147,2,148,2,149,3,0,10,2,150,2,151,2,22,3,58,2,3,152,2,3,59,2,2,153,-57,2,8,2,154,-7,2,18,2,0,2,60,-4,2,0,0,1065361407,0,16384,-9,2,18,2,60,2,0,2,133,-14,2,18,2,133,-6,2,18,0,81919,-15,2,155,3,0,6,2,126,-1,3,0,2,0,2063,-37,2,62,2,156,2,157,2,158,2,159,2,160,-138,3,0,1335,-1,3,0,129,2,32,3,0,6,2,10,3,0,180,2,161,3,0,233,2,162,3,0,18,2,10,-77,3,0,16,2,10,-47,3,0,154,2,6,3,0,130,2,25,-28386,2,0,0,1,-1,2,55,2,0,0,8193,-21,2,201,0,10255,0,4,-11,2,69,2,182,-1,0,71680,-1,2,174,0,4292900864,0,268435519,-5,2,163,-1,2,173,-1,0,6144,-2,2,46,-1,2,168,-1,0,2147532800,2,164,2,170,0,8355840,-2,0,4,-4,2,198,0,205128192,0,1333757536,0,2147483696,0,423953,0,747766272,0,2717763192,0,4286578751,0,278545,2,165,0,4294886464,0,33292336,0,417809,2,165,0,1327482464,0,4278190128,0,700594195,0,1006647527,0,4286497336,0,4160749631,2,166,0,201327104,0,3634348576,0,8323120,2,166,0,202375680,0,2678047264,0,4293984304,2,166,-1,0,983584,0,48,0,58720273,0,3489923072,0,10517376,0,4293066815,0,1,2,213,2,167,2,0,0,2089,0,3221225552,0,201359520,2,0,-2,0,256,0,122880,0,16777216,2,163,0,4160757760,2,0,-6,2,179,-11,0,3263218176,-1,0,49664,0,2160197632,0,8388802,-1,0,12713984,-1,2,168,2,186,2,187,-2,2,175,-20,0,3758096385,-2,2,169,2,195,2,94,2,180,0,4294057984,-2,2,176,2,172,0,4227874816,-2,2,169,-1,2,170,-1,2,181,2,55,0,4026593280,0,14,0,4292919296,-1,2,178,0,939588608,-1,0,805306368,-1,2,55,2,171,2,172,2,173,2,211,2,0,-2,0,8192,-4,0,267386880,-1,0,117440512,0,7168,-1,2,170,2,168,2,174,2,188,-16,2,175,-1,0,1426112704,2,176,-1,2,196,0,271581216,0,2149777408,2,25,2,174,2,55,0,851967,2,189,-1,2,177,2,190,-4,2,178,-20,2,98,2,208,-56,0,3145728,2,191,-10,0,32505856,-1,2,179,-1,0,2147385088,2,94,1,2155905152,2,-3,2,176,2,0,0,67108864,-2,2,180,-6,2,181,2,25,0,1,-1,0,1,-1,2,182,-3,2,126,2,69,-2,2,100,-2,0,32704,2,55,-915,2,183,-1,2,207,-10,2,194,-5,2,185,-6,0,3759456256,2,19,-1,2,184,-1,2,185,-2,0,4227874752,-3,0,2146435072,2,186,-2,0,1006649344,2,55,-1,2,94,0,201375744,-3,0,134217720,2,94,0,4286677377,0,32896,-1,2,178,-3,0,4227907584,-349,0,65520,0,1920,2,167,3,0,264,-11,2,173,-2,2,187,2,0,0,520617856,0,2692743168,0,36,-3,0,524280,-13,2,193,-1,0,4294934272,2,25,2,187,-1,2,215,0,2158720,-3,2,186,0,1,-4,2,55,0,3808625411,0,3489628288,0,4096,0,1207959680,0,3221274624,2,0,-3,2,188,0,120,0,7340032,-2,2,189,2,4,2,25,2,176,3,0,4,2,186,-1,2,190,2,167,-1,0,8176,2,170,2,188,0,1073741824,-1,0,4290773232,2,0,-4,2,176,2,197,0,15728640,2,167,-1,2,174,-1,0,134250480,0,4720640,0,3825467396,-1,2,180,-9,2,94,2,181,0,4294967040,2,137,0,4160880640,3,0,2,0,704,0,1849688064,2,191,-1,2,55,0,4294901887,2,0,0,130547712,0,1879048192,2,212,3,0,2,-1,2,192,2,193,-1,0,17829776,0,2025848832,0,4261477888,-2,2,0,-1,0,4286580608,-1,0,29360128,2,200,0,16252928,0,3791388672,2,130,3,0,2,-2,2,206,2,0,-1,2,107,-1,0,66584576,-1,2,199,-1,0,448,0,4294918080,3,0,6,2,55,-1,0,4294755328,0,4294967267,2,7,-1,2,174,2,187,2,25,2,98,2,25,2,194,2,94,-2,0,245760,2,195,-1,2,163,2,202,0,4227923456,-1,2,196,2,174,2,94,-3,0,4292870145,0,262144,-1,2,95,2,0,0,1073758848,2,197,-1,0,4227921920,2,198,0,68289024,0,528402016,0,4292927536,0,46080,2,191,0,4265609306,0,4294967289,-2,0,268435456,2,95,-2,2,199,3,0,5,-1,2,200,2,176,2,0,-2,0,4227923936,2,67,-1,2,187,2,197,2,99,2,168,2,178,2,204,3,0,5,-1,2,167,3,0,3,-2,0,2146959360,0,9440640,0,104857600,0,4227923840,3,0,2,0,768,2,201,2,28,-2,2,174,-2,2,202,-1,2,169,2,98,3,0,5,-1,0,4227923964,0,512,0,8388608,2,203,2,183,2,193,0,4286578944,3,0,2,0,1152,0,1266679808,2,199,0,576,0,4261707776,2,98,3,0,9,2,169,0,131072,0,939524096,2,188,3,0,2,2,16,-1,0,2147221504,-28,2,187,3,0,3,-3,0,4292902912,-6,2,99,3,0,81,2,25,-2,2,107,-33,2,18,2,181,-124,2,188,-18,2,204,3,0,213,-1,2,187,3,0,54,-17,2,169,2,55,2,205,-1,2,55,2,197,0,4290822144,-2,0,67174336,0,520093700,2,18,3,0,13,-1,2,187,3,0,6,-2,2,188,3,0,3,-2,0,30720,-1,0,32512,3,0,2,0,4294770656,-191,2,185,-38,2,181,2,8,2,206,3,0,278,0,2417033215,-9,0,4294705144,0,4292411391,0,65295,-11,2,167,3,0,72,-3,0,3758159872,0,201391616,3,0,123,-7,2,187,-13,2,180,3,0,2,-1,2,173,2,207,-3,2,99,2,0,-7,2,181,-1,0,384,-1,0,133693440,-3,2,208,-2,2,110,3,0,3,3,180,2,-2,2,94,2,169,3,0,4,-2,2,196,-1,2,163,0,335552923,2,209,-1,0,538974272,0,2214592512,0,132e3,-10,0,192,-8,2,210,-21,0,134213632,2,162,3,0,34,2,55,0,4294965279,3,0,6,0,100663424,0,63524,-1,2,214,2,152,3,0,3,-1,0,3221282816,0,4294917120,3,0,9,2,25,2,211,-1,2,212,3,0,14,2,25,2,187,3,0,6,2,25,2,213,3,0,15,0,2147520640,-6,0,4286578784,2,0,-2,0,1006694400,3,0,24,2,36,-1,0,4292870144,3,0,2,0,1,2,176,3,0,6,2,209,0,4110942569,0,1432950139,0,2701658217,0,4026532864,0,4026532881,2,0,2,47,3,0,8,-1,2,178,-2,2,180,0,98304,0,65537,2,181,-5,2,214,2,0,2,37,2,202,2,167,0,4294770176,2,110,3,0,4,-30,2,192,0,3758153728,-3,0,125829120,-2,2,187,0,4294897664,2,178,-1,2,199,-1,2,174,0,4026580992,2,95,2,0,-10,2,180,0,3758145536,0,31744,-1,0,1610628992,0,4261477376,-4,2,215,-2,2,187,3,0,32,-1335,2,0,-129,2,187,-6,2,176,-180,0,65532,-233,2,177,-18,2,176,3,0,77,-16,2,176,3,0,47,-154,2,170,-130,2,18,3,0,22250,-7,2,18,3,0,6128],[4294967295,4294967291,4092460543,4294828031,4294967294,134217726,4294903807,268435455,2147483647,1048575,1073741823,3892314111,134217727,1061158911,536805376,4294910143,4294901759,32767,4294901760,262143,536870911,8388607,4160749567,4294902783,4294918143,65535,67043328,2281701374,4294967264,2097151,4194303,255,67108863,4294967039,511,524287,131071,63,127,3238002687,4294549487,4290772991,33554431,4294901888,4286578687,67043329,4294705152,4294770687,67043583,1023,15,2047999,67043343,67051519,16777215,2147483648,4294902e3,28,4292870143,4294966783,16383,67047423,4294967279,262083,20511,41943039,493567,4294959104,603979775,65536,602799615,805044223,4294965206,8191,1031749119,4294917631,2134769663,4286578493,4282253311,4294942719,33540095,4294905855,2868854591,1608515583,265232348,534519807,2147614720,1060109444,4093640016,17376,2139062143,224,4169138175,4294909951,4286578688,4294967292,4294965759,535511039,4294966272,4294967280,32768,8289918,4294934399,4294901775,4294965375,1602223615,4294967259,4294443008,268369920,4292804608,4294967232,486341884,4294963199,3087007615,1073692671,4128527,4279238655,4294902015,4160684047,4290246655,469499899,4294967231,134086655,4294966591,2445279231,3670015,31,4294967288,4294705151,3221208447,4294902271,4294549472,4294921215,4095,4285526655,4294966527,4294966143,64,4294966719,3774873592,1877934080,262151,2555904,536807423,67043839,3758096383,3959414372,3755993023,2080374783,4294835295,4294967103,4160749565,4294934527,4087,2016,2147446655,184024726,2862017156,1593309078,268434431,268434414,4294901763,4294901761,536870912,2952790016,202506752,139264,4026531840,402653184,4261412864,63488,1610612736,4227922944,49152,65280,3233808384,3221225472,65534,61440,57152,4293918720,4290772992,25165824,57344,4227915776,4278190080,3758096384,4227858432,4160749568,3758129152,4294836224,4194304,251658240,196608,4294963200,2143289344,2097152,64512,417808,4227923712,12582912,50331648,65528,65472,4294967168,15360,4294966784,65408,4294965248,16,12288,4294934528,2080374784,2013265920,4294950912,524288]),$n=e=>(Le[(e>>>5)+0]>>>e&31&1)!==0,Ie=e=>(Le[(e>>>5)+34816]>>>e&31&1)!==0;function m(e){return e.column++,e.currentChar=e.source.charCodeAt(++e.index)}function x2(e){let n=e.currentChar;if((n&64512)!==55296)return 0;let t=e.source.charCodeAt(e.index+1);return(t&64512)!==56320?0:65536+((n&1023)<<10)+(t&1023)}function p2(e,n){e.currentChar=e.source.charCodeAt(++e.index),e.flags|=1,(n&4)===0&&(e.column=0,e.line++)}function o2(e){e.flags|=1,e.currentChar=e.source.charCodeAt(++e.index),e.column=0,e.line++}function Wn(e){return e===160||e===65279||e===133||e===5760||e>=8192&&e<=8203||e===8239||e===8287||e===12288||e===8201||e===65519}function _(e){return e<65?e-48:e-65+10&15}function Yn(e){switch(e){case 134283266:return"NumericLiteral";case 134283267:return"StringLiteral";case 86021:case 86022:return"BooleanLiteral";case 86023:return"NullLiteral";case 65540:return"RegularExpression";case 67174408:case 67174409:case 131:return"TemplateLiteral";default:return(e&143360)===143360?"Identifier":(e&4096)===4096?"Keyword":"Punctuator"}}var B=[0,0,0,0,0,0,0,0,0,0,1032,0,0,2056,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,8192,0,3,0,0,8192,0,0,0,256,0,33024,0,0,242,242,114,114,114,114,114,114,594,594,0,0,16384,0,0,0,0,67,67,67,67,67,67,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,0,1,0,0,4099,0,71,71,71,71,71,71,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,16384,0,0,0,0],Qn=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,0,0,0,0,1,0,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,0,0,0,0,0],qe=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,1,1,1,1,1,1,1,1,1,1,0,0,0,0,0,0,0,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,0,0,0,0,1,0,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,0,0,0,0,0];function q2(e){return e<=127?Qn[e]>0:Ie(e)}function F2(e){return e<=127?qe[e]>0:$n(e)||e===8204||e===8205}var Pe=["SingleLine","MultiLine","HTMLOpen","HTMLClose","HashbangComment"];function Zn(e){let{source:n}=e;e.currentChar===35&&n.charCodeAt(e.index+1)===33&&(m(e),m(e),ee(e,n,0,4,e.tokenIndex,e.tokenLine,e.tokenColumn))}function Ee(e,n,t,u,o,i,l,c){return u&512&&f(e,0),ee(e,n,t,o,i,l,c)}function ee(e,n,t,u,o,i,l){let{index:c}=e;for(e.tokenIndex=e.index,e.tokenLine=e.line,e.tokenColumn=e.column;e.index<e.end;){if(B[e.currentChar]&8){let d=e.currentChar===13;o2(e),d&&e.index<e.end&&e.currentChar===10&&(e.currentChar=n.charCodeAt(++e.index));break}else if((e.currentChar^8232)<=1){o2(e);break}m(e),e.tokenIndex=e.index,e.tokenLine=e.line,e.tokenColumn=e.column}if(e.options.onComment){let d={start:{line:i,column:l},end:{line:e.tokenLine,column:e.tokenColumn}};e.options.onComment(Pe[u&255],n.slice(c,e.tokenIndex),o,e.tokenIndex,d)}return t|1}function Gn(e,n,t){let{index:u}=e;for(;e.index<e.end;)if(e.currentChar<43){let o=!1;for(;e.currentChar===42;)if(o||(t&=-5,o=!0),m(e)===47){if(m(e),e.options.onComment){let i={start:{line:e.tokenLine,column:e.tokenColumn},end:{line:e.line,column:e.column}};e.options.onComment(Pe[1],n.slice(u,e.index-2),u-2,e.index,i)}return e.tokenIndex=e.index,e.tokenLine=e.line,e.tokenColumn=e.column,t}if(o)continue;B[e.currentChar]&8?e.currentChar===13?(t|=5,o2(e)):(p2(e,t),t=t&-5|1):m(e)}else(e.currentChar^8232)<=1?(t=t&-5|1,o2(e)):(t&=-5,m(e));f(e,18)}var x;(function(e){e[e.Empty=0]="Empty",e[e.Escape=1]="Escape",e[e.Class=2]="Class"})(x||(x={}));var P;(function(e){e[e.Empty=0]="Empty",e[e.IgnoreCase=1]="IgnoreCase",e[e.Global=2]="Global",e[e.Multiline=4]="Multiline",e[e.Unicode=16]="Unicode",e[e.Sticky=8]="Sticky",e[e.DotAll=32]="DotAll",e[e.Indices=64]="Indices",e[e.UnicodeSets=128]="UnicodeSets"})(P||(P={}));function xn(e,n){let t=e.index,u=x.Empty;e:for(;;){let a=e.currentChar;if(m(e),u&x.Escape)u&=~x.Escape;else switch(a){case 47:if(u)break;break e;case 92:u|=x.Escape;break;case 91:u|=x.Class;break;case 93:u&=x.Escape;break}if((a===13||a===10||a===8232||a===8233)&&f(e,34),e.index>=e.source.length)return f(e,34)}let o=e.index-1,i=P.Empty,l=e.currentChar,{index:c}=e;for(;F2(l);){switch(l){case 103:i&P.Global&&f(e,36,"g"),i|=P.Global;break;case 105:i&P.IgnoreCase&&f(e,36,"i"),i|=P.IgnoreCase;break;case 109:i&P.Multiline&&f(e,36,"m"),i|=P.Multiline;break;case 117:i&P.Unicode&&f(e,36,"u"),i&P.UnicodeSets&&f(e,36,"vu"),i|=P.Unicode;break;case 118:i&P.Unicode&&f(e,36,"uv"),i&P.UnicodeSets&&f(e,36,"v"),i|=P.UnicodeSets;break;case 121:i&P.Sticky&&f(e,36,"y"),i|=P.Sticky;break;case 115:i&P.DotAll&&f(e,36,"s"),i|=P.DotAll;break;case 100:i&P.Indices&&f(e,36,"d"),i|=P.Indices;break;default:f(e,35)}l=m(e)}let d=e.source.slice(c,e.index),g=e.source.slice(t,o);return e.tokenRegExp={pattern:g,flags:d},n&128&&(e.tokenRaw=e.source.slice(e.tokenIndex,e.index)),e.tokenValue=pn(e,g,d),65540}function pn(e,n,t){try{return new RegExp(n,t)}catch{try{return new RegExp(n,t),null}catch{f(e,34)}}}function et(e,n,t){let{index:u}=e,o="",i=m(e),l=e.index;for(;(B[i]&8)===0;){if(i===t)return o+=e.source.slice(l,e.index),m(e),n&128&&(e.tokenRaw=e.source.slice(u,e.index)),e.tokenValue=o,134283267;if((i&8)===8&&i===92){if(o+=e.source.slice(l,e.index),i=m(e),i<127||i===8232||i===8233){let c=ve(e,n,i);c>=0?o+=String.fromCodePoint(c):Oe(e,c,0)}else o+=String.fromCodePoint(i);l=e.index+1}else(i===8232||i===8233)&&(e.column=-1,e.line++);e.index>=e.end&&f(e,16),i=m(e)}f(e,16)}function ve(e,n,t,u=0){switch(t){case 98:return 8;case 102:return 12;case 114:return 13;case 110:return 10;case 116:return 9;case 118:return 11;case 13:if(e.index<e.end){let o=e.source.charCodeAt(e.index+1);o===10&&(e.index=e.index+1,e.currentChar=o)}case 10:case 8232:case 8233:return e.column=-1,e.line++,-1;case 48:case 49:case 50:case 51:{let o=t-48,i=e.index+1,l=e.column+1;if(i<e.end){let c=e.source.charCodeAt(i);if((B[c]&32)===0){if(o!==0||B[c]&512){if(n&256||u)return-2;e.flags|=64}}else{if(n&256||u)return-2;if(e.currentChar=c,o=o<<3|c-48,i++,l++,i<e.end){let d=e.source.charCodeAt(i);B[d]&32&&(e.currentChar=d,o=o<<3|d-48,i++,l++)}e.flags|=64}e.index=i-1,e.column=l-1}return o}case 52:case 53:case 54:case 55:{if(u||n&256)return-2;let o=t-48,i=e.index+1,l=e.column+1;if(i<e.end){let c=e.source.charCodeAt(i);B[c]&32&&(o=o<<3|c-48,e.currentChar=c,e.index=i,e.column=l)}return e.flags|=64,o}case 120:{let o=m(e);if((B[o]&64)===0)return-4;let i=_(o),l=m(e);if((B[l]&64)===0)return-4;let c=_(l);return i<<4|c}case 117:{let o=m(e);if(e.currentChar===123){let i=0;for(;(B[m(e)]&64)!==0;)if(i=i<<4|_(e.currentChar),i>1114111)return-5;return e.currentChar<1||e.currentChar!==125?-4:i}else{if((B[o]&64)===0)return-4;let i=e.source.charCodeAt(e.index+1);if((B[i]&64)===0)return-4;let l=e.source.charCodeAt(e.index+2);if((B[l]&64)===0)return-4;let c=e.source.charCodeAt(e.index+3);return(B[c]&64)===0?-4:(e.index+=3,e.column+=3,e.currentChar=e.source.charCodeAt(e.index),_(o)<<12|_(i)<<8|_(l)<<4|_(c))}}case 56:case 57:if(u||(n&64)===0||n&256)return-3;e.flags|=4096;default:return t}}function Oe(e,n,t){switch(n){case-1:return;case-2:f(e,t?2:1);case-3:f(e,t?3:14);case-4:f(e,7);case-5:f(e,104)}}function Re(e,n){let{index:t}=e,u=67174409,o="",i=m(e);for(;i!==96;){if(i===36&&e.source.charCodeAt(e.index+1)===123){m(e),u=67174408;break}else if(i===92)if(i=m(e),i>126)o+=String.fromCodePoint(i);else{let{index:l,line:c,column:d}=e,g=ve(e,n|256,i,1);if(g>=0)o+=String.fromCodePoint(g);else if(g!==-1&&n&16384){e.index=l,e.line=c,e.column=d,o=null,i=nt(e,i),i<0&&(u=67174408);break}else Oe(e,g,1)}else e.index<e.end&&(i===13&&e.source.charCodeAt(e.index)===10&&(o+=String.fromCodePoint(i),e.currentChar=e.source.charCodeAt(++e.index)),((i&83)<3&&i===10||(i^8232)<=1)&&(e.column=-1,e.line++),o+=String.fromCodePoint(i));e.index>=e.end&&f(e,17),i=m(e)}return m(e),e.tokenValue=o,e.tokenRaw=e.source.slice(t+1,e.index-(u===67174409?1:2)),u}function nt(e,n){for(;n!==96;){switch(n){case 36:{let t=e.index+1;if(t<e.end&&e.source.charCodeAt(t)===123)return e.index=t,e.column++,-n;break}case 10:case 8232:case 8233:e.column=-1,e.line++}e.index>=e.end&&f(e,17),n=m(e)}return n}function tt(e,n){return e.index>=e.end&&f(e,0),e.index--,e.column--,Re(e,n)}function re(e,n,t){let u=e.currentChar,o=0,i=9,l=t&64?0:1,c=0,d=0;if(t&64)o="."+B2(e,u),u=e.currentChar,u===110&&f(e,12);else{if(u===48)if(u=m(e),(u|32)===120){for(t=136,u=m(e);B[u]&4160;){if(u===95){d||f(e,152),d=0,u=m(e);continue}d=1,o=o*16+_(u),c++,u=m(e)}(c===0||!d)&&f(e,c===0?21:153)}else if((u|32)===111){for(t=132,u=m(e);B[u]&4128;){if(u===95){d||f(e,152),d=0,u=m(e);continue}d=1,o=o*8+(u-48),c++,u=m(e)}(c===0||!d)&&f(e,c===0?0:153)}else if((u|32)===98){for(t=130,u=m(e);B[u]&4224;){if(u===95){d||f(e,152),d=0,u=m(e);continue}d=1,o=o*2+(u-48),c++,u=m(e)}(c===0||!d)&&f(e,c===0?0:153)}else if(B[u]&32)for(n&256&&f(e,1),t=1;B[u]&16;){if(B[u]&512){t=32,l=0;break}o=o*8+(u-48),u=m(e)}else B[u]&512?(n&256&&f(e,1),e.flags|=64,t=32):u===95&&f(e,0);if(t&48){if(l){for(;i>=0&&B[u]&4112;){if(u===95){u=m(e),(u===95||t&32)&&a2(e.currentLocation,{index:e.index+1,line:e.line,column:e.column},152),d=1;continue}d=0,o=10*o+(u-48),u=m(e),--i}if(d&&a2(e.currentLocation,{index:e.index+1,line:e.line,column:e.column},153),i>=0&&!q2(u)&&u!==46)return e.tokenValue=o,n&128&&(e.tokenRaw=e.source.slice(e.tokenIndex,e.index)),134283266}o+=B2(e,u),u=e.currentChar,u===46&&(m(e)===95&&f(e,0),t=64,o+="."+B2(e,e.currentChar),u=e.currentChar)}}let g=e.index,a=0;if(u===110&&t&128)a=1,u=m(e);else if((u|32)===101){u=m(e),B[u]&256&&(u=m(e));let{index:s}=e;(B[u]&16)===0&&f(e,11),o+=e.source.substring(g,s)+B2(e,u),u=e.currentChar}return(e.index<e.end&&B[u]&16||q2(u))&&f(e,13),a?(e.tokenRaw=e.source.slice(e.tokenIndex,e.index),e.tokenValue=BigInt(G(!1,e.tokenRaw.slice(0,-1),"_","")),134283388):(e.tokenValue=t&15?o:t&32?parseFloat(e.source.substring(e.tokenIndex,e.index)):+o,n&128&&(e.tokenRaw=e.source.slice(e.tokenIndex,e.index)),134283266)}function B2(e,n){let t=0,u=e.index,o="";for(;B[n]&4112;){if(n===95){let{index:i}=e;n=m(e),n===95&&a2(e.currentLocation,{index:e.index+1,line:e.line,column:e.column},152),t=1,o+=e.source.substring(u,i),u=e.index;continue}t=0,n=m(e)}return t&&a2(e.currentLocation,{index:e.index+1,line:e.line,column:e.column},153),o+e.source.substring(u,e.index)}var S=["end of source","identifier","number","string","regular expression","false","true","null","template continuation","template tail","=>","(","{",".","...","}",")",";",",","[","]",":","?","'",'"',"++","--","=","<<=",">>=",">>>=","**=","+=","-=","*=","/=","%=","^=","|=","&=","||=","&&=","??=","typeof","delete","void","!","~","+","-","in","instanceof","*","%","/","**","&&","||","===","!==","==","!=","<=",">=","<",">","<<",">>",">>>","&","|","^","var","let","const","break","case","catch","class","continue","debugger","default","do","else","export","extends","finally","for","function","if","import","new","return","super","switch","this","throw","try","while","with","implements","interface","package","private","protected","public","static","yield","as","async","await","constructor","get","set","accessor","from","of","enum","eval","arguments","escaped keyword","escaped future reserved keyword","reserved if strict","#","BigIntLiteral","??","?.","WhiteSpace","Illegal","LineTerminator","PrivateField","Template","@","target","meta","LineFeed","Escaped","JSXText"],Ve=Object.create(null,{this:{value:86111},function:{value:86104},if:{value:20569},return:{value:20572},var:{value:86088},else:{value:20563},for:{value:20567},new:{value:86107},in:{value:8673330},typeof:{value:16863275},while:{value:20578},case:{value:20556},break:{value:20555},try:{value:20577},catch:{value:20557},delete:{value:16863276},throw:{value:86112},switch:{value:86110},continue:{value:20559},default:{value:20561},instanceof:{value:8411187},do:{value:20562},void:{value:16863277},finally:{value:20566},async:{value:209005},await:{value:209006},class:{value:86094},const:{value:86090},constructor:{value:12399},debugger:{value:20560},export:{value:20564},extends:{value:20565},false:{value:86021},from:{value:209011},get:{value:209008},implements:{value:36964},import:{value:86106},interface:{value:36965},let:{value:241737},null:{value:86023},of:{value:471156},package:{value:36966},private:{value:36967},protected:{value:36968},public:{value:36969},set:{value:209009},static:{value:36970},super:{value:86109},true:{value:86022},with:{value:20579},yield:{value:241771},enum:{value:86133},eval:{value:537079926},as:{value:77932},arguments:{value:537079927},target:{value:209029},meta:{value:209030},accessor:{value:12402}});function we(e,n,t){for(;qe[m(e)];);return e.tokenValue=e.source.slice(e.tokenIndex,e.index),e.currentChar!==92&&e.currentChar<=126?Ve[e.tokenValue]||208897:ne(e,n,0,t)}function ut(e,n){let t=Me(e);return q2(t)||f(e,5),e.tokenValue=String.fromCodePoint(t),ne(e,n,1,B[t]&4)}function ne(e,n,t,u){let o=e.index;for(;e.index<e.end;)if(e.currentChar===92){e.tokenValue+=e.source.slice(o,e.index),t=1;let l=Me(e);F2(l)||f(e,5),u=u&&B[l]&4,e.tokenValue+=String.fromCodePoint(l),o=e.index}else{let l=x2(e);if(l>0)F2(l)||f(e,20,String.fromCodePoint(l)),e.currentChar=l,e.index++,e.column++;else if(!F2(e.currentChar))break;m(e)}e.index<=e.end&&(e.tokenValue+=e.source.slice(o,e.index));let{length:i}=e.tokenValue;if(u&&i>=2&&i<=11){let l=Ve[e.tokenValue];return l===void 0?208897|(t?-2147483648:0):t?l===209006?(n&524800)===0?l|-2147483648:-2147483528:n&256?l===36970||(l&36864)===36864?-2147483527:(l&20480)===20480?n&67108864&&(n&2048)===0?l|-2147483648:-2147483528:-2147274630:n&67108864&&(n&2048)===0&&(l&20480)===20480?l|-2147483648:l===241771?n&67108864?-2147274630:n&262144?-2147483528:l|-2147483648:l===209005?-2147274630:(l&36864)===36864?l|12288|-2147483648:-2147483528:l}return 208897|(t?-2147483648:0)}function it(e){let n=m(e);if(n===92)return 130;let t=x2(e);return t&&(n=t),q2(n)||f(e,96),130}function Me(e){return e.source.charCodeAt(e.index+1)!==117&&f(e,5),e.currentChar=e.source.charCodeAt(e.index+=2),e.column+=2,ot(e)}function ot(e){let n=0,t=e.currentChar;if(t===123){let l=e.index-2;for(;B[m(e)]&64;)n=n<<4|_(e.currentChar),n>1114111&&a2({index:l,line:e.line,column:e.column},e.currentLocation,104);return e.currentChar!==125&&a2({index:l,line:e.line,column:e.column},e.currentLocation,7),m(e),n}(B[t]&64)===0&&f(e,7);let u=e.source.charCodeAt(e.index+1);(B[u]&64)===0&&f(e,7);let o=e.source.charCodeAt(e.index+2);(B[o]&64)===0&&f(e,7);let i=e.source.charCodeAt(e.index+3);return(B[i]&64)===0&&f(e,7),n=_(t)<<12|_(u)<<8|_(o)<<4|_(i),e.currentChar=e.source.charCodeAt(e.index+=4),e.column+=4,n}var lt=[128,128,128,128,128,128,128,128,128,127,135,127,127,129,128,128,128,128,128,128,128,128,128,128,128,128,128,128,128,128,128,128,127,16842798,134283267,130,208897,8391477,8390213,134283267,67174411,16,8391476,25233968,18,25233969,67108877,8457014,134283266,134283266,134283266,134283266,134283266,134283266,134283266,134283266,134283266,134283266,21,1074790417,8456256,1077936155,8390721,22,132,208897,208897,208897,208897,208897,208897,208897,208897,208897,208897,208897,208897,208897,208897,208897,208897,208897,208897,208897,208897,208897,208897,208897,208897,208897,208897,69271571,136,20,8389959,208897,131,4096,4096,4096,4096,4096,4096,4096,208897,4096,208897,208897,4096,208897,4096,208897,4096,208897,4096,4096,4096,208897,4096,4096,208897,4096,4096,2162700,8389702,1074790415,16842799,128];function k(e,n){e.flags=(e.flags|1)^1,e.startIndex=e.index,e.startColumn=e.column,e.startLine=e.line,e.setToken(Ue(e,n,0))}function Ue(e,n,t){let u=e.index===0,{source:o}=e,i=e.index,l=e.line,c=e.column;for(;e.index<e.end;){e.tokenIndex=e.index,e.tokenColumn=e.column,e.tokenLine=e.line;let d=e.currentChar;if(d<=126){let g=lt[d];switch(g){case 67174411:case 16:case 2162700:case 1074790415:case 69271571:case 20:case 21:case 1074790417:case 18:case 16842799:case 132:case 128:return m(e),g;case 208897:return we(e,n,0);case 4096:return we(e,n,1);case 134283266:return re(e,n,144);case 134283267:return et(e,n,d);case 131:return Re(e,n);case 136:return ut(e,n);case 130:return it(e);case 127:m(e);break;case 129:t|=5,o2(e);break;case 135:p2(e,t),t=t&-5|1;break;case 8456256:{let a=m(e);if(e.index<e.end){if(a===60)return e.index<e.end&&m(e)===61?(m(e),4194332):8390978;if(a===61)return m(e),8390718;if(a===33){let s=e.index+1;if(s+1<e.end&&o.charCodeAt(s)===45&&o.charCodeAt(s+1)==45){e.column+=3,e.currentChar=o.charCodeAt(e.index+=3),t=Ee(e,o,t,n,2,e.tokenIndex,e.tokenLine,e.tokenColumn),i=e.tokenIndex,l=e.tokenLine,c=e.tokenColumn;continue}return 8456256}}return 8456256}case 1077936155:{m(e);let a=e.currentChar;return a===61?m(e)===61?(m(e),8390458):8390460:a===62?(m(e),10):1077936155}case 16842798:return m(e)!==61?16842798:m(e)!==61?8390461:(m(e),8390459);case 8391477:return m(e)!==61?8391477:(m(e),4194340);case 8391476:{if(m(e),e.index>=e.end)return 8391476;let a=e.currentChar;return a===61?(m(e),4194338):a!==42?8391476:m(e)!==61?8391735:(m(e),4194335)}case 8389959:return m(e)!==61?8389959:(m(e),4194341);case 25233968:{m(e);let a=e.currentChar;return a===43?(m(e),33619993):a===61?(m(e),4194336):25233968}case 25233969:{m(e);let a=e.currentChar;if(a===45){if(m(e),(t&1||u)&&e.currentChar===62){(n&64)===0&&f(e,112),m(e),t=Ee(e,o,t,n,3,i,l,c),i=e.tokenIndex,l=e.tokenLine,c=e.tokenColumn;continue}return 33619994}return a===61?(m(e),4194337):25233969}case 8457014:{if(m(e),e.index<e.end){let a=e.currentChar;if(a===47){m(e),t=ee(e,o,t,0,e.tokenIndex,e.tokenLine,e.tokenColumn),i=e.tokenIndex,l=e.tokenLine,c=e.tokenColumn;continue}if(a===42){m(e),t=Gn(e,o,t),i=e.tokenIndex,l=e.tokenLine,c=e.tokenColumn;continue}if(n&8192)return xn(e,n);if(a===61)return m(e),4259875}return 8457014}case 67108877:{let a=m(e);if(a>=48&&a<=57)return re(e,n,80);if(a===46){let s=e.index+1;if(s<e.end&&o.charCodeAt(s)===46)return e.column+=2,e.currentChar=o.charCodeAt(e.index+=2),14}return 67108877}case 8389702:{m(e);let a=e.currentChar;return a===124?(m(e),e.currentChar===61?(m(e),4194344):8913465):a===61?(m(e),4194342):8389702}case 8390721:{m(e);let a=e.currentChar;if(a===61)return m(e),8390719;if(a!==62)return 8390721;if(m(e),e.index<e.end){let s=e.currentChar;if(s===62)return m(e)===61?(m(e),4194334):8390980;if(s===61)return m(e),4194333}return 8390979}case 8390213:{m(e);let a=e.currentChar;return a===38?(m(e),e.currentChar===61?(m(e),4194345):8913720):a===61?(m(e),4194343):8390213}case 22:{let a=m(e);if(a===63)return m(e),e.currentChar===61?(m(e),4194346):276824445;if(a===46){let s=e.index+1;if(s<e.end&&(a=o.charCodeAt(s),!(a>=48&&a<=57)))return m(e),67108990}return 22}}}else{if((d^8232)<=1){t=t&-5|1,o2(e);continue}let g=x2(e);if(g>0&&(d=g),Ie(d))return e.tokenValue="",ne(e,n,0,0);if(Wn(d)){m(e);continue}f(e,20,String.fromCodePoint(d))}}return 1048576}function ft(e,n){return e.startIndex=e.tokenIndex=e.index,e.startColumn=e.tokenColumn=e.column,e.startLine=e.tokenLine=e.line,e.setToken(B[e.currentChar]&8192?ct(e,n):Ue(e,n,0)),e.getToken()}function ct(e,n){let t=e.currentChar,u=m(e),o=e.index;for(;u!==t;)e.index>=e.end&&f(e,16),u=m(e);return u!==t&&f(e,16),e.tokenValue=e.source.slice(o,e.index),m(e),n&128&&(e.tokenRaw=e.source.slice(e.tokenIndex,e.index)),134283267}function T2(e,n){if(e.startIndex=e.tokenIndex=e.index,e.startColumn=e.tokenColumn=e.column,e.startLine=e.tokenLine=e.line,e.index>=e.end){e.setToken(1048576);return}if(e.currentChar===60){m(e),e.setToken(8456256);return}if(e.currentChar===123){m(e),e.setToken(2162700);return}let t=0;for(;e.index<e.end;){let o=B[e.source.charCodeAt(e.index)];if(o&1024?(t|=5,o2(e)):o&2048?(p2(e,t),t=t&-5|1):m(e),B[e.currentChar]&16384)break}e.tokenIndex===e.index&&f(e,0);let u=e.source.slice(e.tokenIndex,e.index);n&128&&(e.tokenRaw=u),e.tokenValue=u,e.setToken(137)}function $2(e){if((e.getToken()&143360)===143360){let{index:n}=e,t=e.currentChar;for(;B[t]&32770;)t=m(e);e.tokenValue+=e.source.slice(n,e.index),e.setToken(208897,!0)}return e.getToken()}function U(e,n){var t,u;(e.flags&1)===0&&(e.getToken()&1048576)!==1048576&&f(e,30,S[e.getToken()&255]),r(e,n,1074790417)||(u=(t=e.options).onInsertedSemicolon)==null||u.call(t,e.startIndex)}function Je(e,n,t,u){return n-t<13&&u==="use strict"&&((e.getToken()&1048576)===1048576||e.flags&1)?1:0}function te(e,n,t){return e.getToken()!==t?0:(k(e,n),1)}function r(e,n,t){return e.getToken()!==t?!1:(k(e,n),!0)}function A(e,n,t){e.getToken()!==t&&f(e,25,S[t&255]),k(e,n)}function $(e,n){switch(n.type){case"ArrayExpression":{n.type="ArrayPattern";let{elements:t}=n;for(let u=0,o=t.length;u<o;++u){let i=t[u];i&&$(e,i)}return}case"ObjectExpression":{n.type="ObjectPattern";let{properties:t}=n;for(let u=0,o=t.length;u<o;++u)$(e,t[u]);return}case"AssignmentExpression":n.type="AssignmentPattern",n.operator!=="="&&f(e,71),delete n.operator,$(e,n.left);return;case"Property":$(e,n.value);return;case"SpreadElement":n.type="RestElement",$(e,n.argument)}}function P2(e,n,t,u,o){n&256&&((u&36864)===36864&&f(e,118),!o&&(u&537079808)===537079808&&f(e,119)),((u&20480)===20480||u===-2147483528)&&f(e,102),t&24&&(u&255)===73&&f(e,100),n&524800&&u===209006&&f(e,110),n&262400&&u===241771&&f(e,97,"yield")}function _e(e,n,t){n&256&&((t&36864)===36864&&f(e,118),(t&537079808)===537079808&&f(e,119),t===-2147483527&&f(e,95),t===-2147483528&&f(e,95)),(t&20480)===20480&&f(e,102),n&524800&&t===209006&&f(e,110),n&262400&&t===241771&&f(e,97,"yield")}function je(e,n,t){return t===209006&&(n&524800&&f(e,110),e.destructible|=128),t===241771&&n&262144&&f(e,97,"yield"),(t&20480)===20480||(t&36864)===36864||t==-2147483527}function dt(e){return e.property?e.property.type==="PrivateIdentifier":!1}function Xe(e,n,t,u){for(;n;){if(n["$"+t])return u&&f(e,137),1;u&&n.loop&&(u=0),n=n.$}return 0}function at(e,n,t){let u=n;for(;u;)u["$"+t]&&f(e,136,t),u=u.$;n["$"+t]=1}function v2(e){switch(e.type){case"JSXIdentifier":return e.name;case"JSXNamespacedName":return e.namespace+":"+e.name;case"JSXMemberExpression":return v2(e.object)+"."+v2(e.property)}}function M2(e,n,t){let u=q(l2(),1024);return n2(e,n,u,t,1,0),u}function W2(e,n,...t){return{type:n,params:t,start:e.tokenStart,end:e.currentLocation}}function l2(){return{parent:void 0,type:2}}function q(e,n){return{parent:e,type:n,scopeError:void 0}}function st(e){return{parent:e,refs:Object.create(null)}}function Y(e,n,t,u,o,i){o&4?He(e,n,t,u,o):n2(e,n,t,u,o,i),i&64&&t2(e,u)}function n2(e,n,t,u,o,i){let l=t["#"+u];l&&(l&2)===0&&(o&1?t.scopeError=W2(e,145,u):n&64&&(n&256)===0&&i&2&&l===64&&o===64||f(e,145,u)),t.type&128&&t.parent["#"+u]&&(t.parent["#"+u]&2)===0&&f(e,145,u),t.type&1024&&l&&(l&2)===0&&o&1&&(t.scopeError=W2(e,145,u)),t.type&64&&t.parent["#"+u]&768&&f(e,159,u),t["#"+u]=o}function He(e,n,t,u,o){let i=t;for(;i&&(i.type&256)===0;){let l=i["#"+u];l&248&&(n&64&&(n&256)===0&&(o&128&&l&68||l&128&&o&68)||f(e,145,u)),i===t&&l&1&&o&1&&(i.scopeError=W2(e,145,u)),(l&256||l&512&&(n&64)===0)&&f(e,145,u),i["#"+u]=o,i=i.parent}}function gt(e,n,t,u){let o=u&800;o&768||(o|=768);let i=n["#"+t];i!==void 0&&((i&32)!==(o&32)||i&o&768)&&f(e,146,t),n["#"+t]=i?i|o:o}function mt(e,n,t){var u;(u=n.refs)[t]??(u[t]=[]),n.refs[t].push({index:e.tokenIndex,line:e.tokenLine,column:e.tokenColumn})}function ze(e,n){return n["#"+e]?1:n.parent?ze(e,n.parent):0}function kt(e){for(let n in e.refs)if(!ze(n,e)){let{index:t,line:u,column:o}=e.refs[n][0];throw new i2({index:t,line:u,column:o},{index:t+n.length,line:u,column:o+n.length},4,n)}}function t2(e,n){e.exportedNames!==void 0&&n!==""&&(e.exportedNames["#"+n]&&f(e,147,n),e.exportedNames["#"+n]=1)}function ht(e,n){e.exportedBindings!==void 0&&n!==""&&(e.exportedBindings["#"+n]=1)}function b2(e,n){return e&262400?e&512&&n===209006||e&262144&&n===241771?!1:(n&12288)===12288:(n&12288)===12288||(n&36864)===36864}function U2(e,n,t){(t&537079808)===537079808&&(n&256&&f(e,119),e.flags|=512),b2(n,t)||f(e,0)}var Y2=class{source;options;lastOnToken=null;token=1048576;flags=0;index=0;line=1;column=0;startIndex=0;end=0;tokenIndex=0;startColumn=0;tokenColumn=0;tokenLine=1;startLine=1;tokenValue="";tokenRaw="";tokenRegExp=void 0;currentChar=0;exportedNames={};exportedBindings={};assignable=1;destructible=0;leadingDecorators={decorators:[]};constructor(n,t={}){this.source=n,this.options=t,this.end=n.length,this.currentChar=n.charCodeAt(0)}getToken(){return this.token}setToken(n,t=!1){this.token=n;let{onToken:u}=this.options;if(u)if(n!==1048576){let o={start:{line:this.tokenLine,column:this.tokenColumn},end:{line:this.line,column:this.column}};!t&&this.lastOnToken&&u(...this.lastOnToken),this.lastOnToken=[Yn(n),this.tokenIndex,this.index,o]}else this.lastOnToken&&(u(...this.lastOnToken),this.lastOnToken=null);return n}get tokenStart(){return{index:this.tokenIndex,line:this.tokenLine,column:this.tokenColumn}}get currentLocation(){return{index:this.index,line:this.line,column:this.column}}finishNode(n,t,u){if(this.options.shouldAddRanges){n.start=t.index;let o=u?u.index:this.startIndex;n.end=o,n.range=[t.index,o]}return this.options.shouldAddLoc&&(n.loc={start:{line:t.line,column:t.column},end:u?{line:u.line,column:u.column}:{line:this.startLine,column:this.startColumn}},this.options.sourceFile&&(n.loc.source=this.options.sourceFile)),n}};function yt(e,n){return function(t,u,o,i,l){let c={type:t,value:u};n.shouldAddRanges&&(c.start=o,c.end=i,c.range=[o,i]),n.shouldAddLoc&&(c.loc=l),e.push(c)}}function At(e,n){return function(t,u,o,i){let l={token:t};n.shouldAddRanges&&(l.start=u,l.end=o,l.range=[u,o]),n.shouldAddLoc&&(l.loc=i),e.push(l)}}function Tt(e,n,t){n!=null&&(n.module&&(t|=768),n.next&&(t|=1),n.loc&&(t|=4),n.ranges&&(t|=2),n.uniqueKeyInPattern&&(t|=134217728),n.lexical&&(t|=16),n.webcompat&&(t|=64),n.globalReturn&&(t|=1048576),n.raw&&(t|=128),n.preserveParens&&(t|=32),n.impliedStrict&&(t|=256),n.jsx&&(t|=8));let u={shouldAddLoc:!!(t&4),shouldAddRanges:!!(t&2)};n!=null&&(n.source&&(u.sourceFile=n.source),n.onComment!=null&&(u.onComment=Array.isArray(n.onComment)?yt(n.onComment,u):n.onComment),n.onInsertedSemicolon!=null&&(u.onInsertedSemicolon=n.onInsertedSemicolon),n.onToken!=null&&(u.onToken=Array.isArray(n.onToken)?At(n.onToken,u):n.onToken));let o=new Y2(e,u);Zn(o);let i=t&16?l2():void 0,l=[],c="script";if(t&512){if(c="module",l=Dt(o,t|2048,i),i)for(let d in o.exportedBindings)d[0]==="#"&&!i[d]&&f(o,148,d.slice(1))}else l=bt(o,t|2048,i);return o.finishNode({type:"Program",sourceType:c,body:l},{index:0,line:1,column:0},o.currentLocation)}function bt(e,n,t){k(e,n|8192|67108864);let u=[];for(;e.getToken()===134283267;){let{index:o,tokenValue:i,tokenStart:l,tokenIndex:c}=e,d=e.getToken(),g=v(e,n);Je(e,o,c,i)&&(n|=256,e.flags&64&&J(e.tokenStart,e.currentLocation,9),e.flags&4096&&J(e.tokenStart,e.currentLocation,15)),u.push(ie(e,n,g,d,l))}for(;e.getToken()!==1048576;)u.push(D2(e,n,t,void 0,4,{}));return u}function Dt(e,n,t){k(e,n|8192);let u=[];for(;e.getToken()===134283267;){let{tokenStart:o}=e,i=e.getToken();u.push(ie(e,n,v(e,n),i,o))}for(;e.getToken()!==1048576;)u.push(Ct(e,n,t));return u}function Ct(e,n,t){var o;e.getToken()===132&&Object.assign(e.leadingDecorators,{start:e.tokenStart,decorators:j2(e,n,void 0)});let u;switch(e.getToken()){case 20564:u=jt(e,n,t);break;case 86106:u=Jt(e,n,t);break;default:u=D2(e,n,t,void 0,4,{})}return(o=e.leadingDecorators)!=null&&o.decorators.length&&f(e,170),u}function D2(e,n,t,u,o,i){let l=e.tokenStart;switch(e.getToken()){case 86104:return e2(e,n,t,u,o,1,0,0,l);case 132:case 86094:return G2(e,n,t,u,0);case 86090:return Q2(e,n,t,u,16,0);case 241737:return Mt(e,n,t,u,o);case 20564:f(e,103,"export");case 86106:switch(k(e,n),e.getToken()){case 67174411:return Qe(e,n,u,l);case 67108877:return Ye(e,n,l);default:f(e,103,"import")}case 209005:return Ke(e,n,t,u,o,i,1);default:return C2(e,n,t,u,o,i,1)}}function C2(e,n,t,u,o,i,l){switch(e.getToken()){case 86088:return $e(e,n,t,u,0);case 20572:return rt(e,n,u);case 20569:return St(e,n,t,u,i);case 20567:return Ut(e,n,t,u,i);case 20562:return Vt(e,n,t,u,i);case 20578:return Nt(e,n,t,u,i);case 86110:return Ft(e,n,t,u,i);case 1074790417:return wt(e,n);case 2162700:return A2(e,n,t&&q(t,2),u,i,e.tokenStart);case 86112:return Bt(e,n,u);case 20555:return It(e,n,i);case 20559:return Lt(e,n,i);case 20577:return vt(e,n,t,u,i);case 20579:return qt(e,n,t,u,i);case 20560:return Pt(e,n);case 209005:return Ke(e,n,t,u,o,i,0);case 20557:f(e,162);case 20566:f(e,163);case 86104:f(e,n&256?76:(n&64)===0?78:77);case 86094:f(e,79);default:return Et(e,n,t,u,o,i,l)}}function Et(e,n,t,u,o,i,l){let{tokenValue:c,tokenStart:d}=e,g=e.getToken(),a;switch(g){case 241737:a=N(e,n),n&256&&f(e,85),e.getToken()===69271571&&f(e,84);break;default:a=j(e,n,u,2,0,1,0,1,e.tokenStart)}return g&143360&&e.getToken()===21?ue(e,n,t,u,o,i,c,a,g,l,d):(a=F(e,n,u,a,0,0,d),a=I(e,n,u,0,0,d,a),e.getToken()===18&&(a=Q(e,n,u,0,d,a)),s2(e,n,a,d))}function A2(e,n,t,u,o,i=e.tokenStart,l="BlockStatement"){let c=[];for(A(e,n|8192,2162700);e.getToken()!==1074790415;)c.push(D2(e,n,t,u,2,{$:o}));return A(e,n|8192,1074790415),e.finishNode({type:l,body:c},i)}function rt(e,n,t){(n&1048576)===0&&f(e,92);let u=e.tokenStart;k(e,n|8192);let o=e.flags&1||e.getToken()&1048576?null:V(e,n,t,0,1,e.tokenStart);return U(e,n|8192),e.finishNode({type:"ReturnStatement",argument:o},u)}function s2(e,n,t,u){return U(e,n|8192),e.finishNode({type:"ExpressionStatement",expression:t},u)}function ue(e,n,t,u,o,i,l,c,d,g,a){P2(e,n,0,d,1),at(e,i,l),k(e,n|8192);let s=g&&(n&256)===0&&n&64&&e.getToken()===86104?e2(e,n,q(t,2),u,o,0,0,0,e.tokenStart):C2(e,n,t,u,o,i,g);return e.finishNode({type:"LabeledStatement",label:c,body:s},a)}function Ke(e,n,t,u,o,i,l){let{tokenValue:c,tokenStart:d}=e,g=e.getToken(),a=N(e,n);if(e.getToken()===21)return ue(e,n,t,u,o,i,c,a,g,1,d);let s=e.flags&1;if(!s){if(e.getToken()===86104)return l||f(e,123),e2(e,n,t,u,o,1,0,1,d);if(b2(n,e.getToken()))return a=tn(e,n,u,1,d),e.getToken()===18&&(a=Q(e,n,u,0,d,a)),s2(e,n,a,d)}return e.getToken()===67174411?a=ae(e,n,u,a,1,1,0,s,d):(e.getToken()===10&&(U2(e,n,g),(g&36864)===36864&&(e.flags|=256),a=_2(e,n|524288,u,e.tokenValue,a,0,1,0,d)),e.assignable=1),a=F(e,n,u,a,0,0,d),a=I(e,n,u,0,0,d,a),e.assignable=1,e.getToken()===18&&(a=Q(e,n,u,0,d,a)),s2(e,n,a,d)}function ie(e,n,t,u,o){let i=e.startIndex;u!==1074790417&&(e.assignable=2,t=F(e,n,void 0,t,0,0,o),e.getToken()!==1074790417&&(t=I(e,n,void 0,0,0,o,t),e.getToken()===18&&(t=Q(e,n,void 0,0,o,t))),U(e,n|8192));let l={type:"ExpressionStatement",expression:t};return t.type==="Literal"&&typeof t.value=="string"&&(l.directive=e.source.slice(o.index+1,i-1)),e.finishNode(l,o)}function wt(e,n){let t=e.tokenStart;return k(e,n|8192),e.finishNode({type:"EmptyStatement"},t)}function Bt(e,n,t){let u=e.tokenStart;k(e,n|8192),e.flags&1&&f(e,90);let o=V(e,n,t,0,1,e.tokenStart);return U(e,n|8192),e.finishNode({type:"ThrowStatement",argument:o},u)}function St(e,n,t,u,o){let i=e.tokenStart;k(e,n),A(e,n|8192,67174411),e.assignable=1;let l=V(e,n,u,0,1,e.tokenStart);A(e,n|8192,16);let c=Be(e,n,t,u,o),d=null;return e.getToken()===20563&&(k(e,n|8192),d=Be(e,n,t,u,o)),e.finishNode({type:"IfStatement",test:l,consequent:c,alternate:d},i)}function Be(e,n,t,u,o){let{tokenStart:i}=e;return n&256||(n&64)===0||e.getToken()!==86104?C2(e,n,t,u,0,{$:o},0):e2(e,n,q(t,2),u,0,0,0,0,i)}function Ft(e,n,t,u,o){let i=e.tokenStart;k(e,n),A(e,n|8192,67174411);let l=V(e,n,u,0,1,e.tokenStart);A(e,n,16),A(e,n,2162700);let c=[],d=0;for(t&&(t=q(t,8));e.getToken()!==1074790415;){let{tokenStart:g}=e,a=null,s=[];for(r(e,n|8192,20556)?a=V(e,n,u,0,1,e.tokenStart):(A(e,n|8192,20561),d&&f(e,89),d=1),A(e,n|8192,21);e.getToken()!==20556&&e.getToken()!==1074790415&&e.getToken()!==20561;)s.push(D2(e,n|1024,t,u,2,{$:o}));c.push(e.finishNode({type:"SwitchCase",test:a,consequent:s},g))}return A(e,n|8192,1074790415),e.finishNode({type:"SwitchStatement",discriminant:l,cases:c},i)}function Nt(e,n,t,u,o){let i=e.tokenStart;k(e,n),A(e,n|8192,67174411);let l=V(e,n,u,0,1,e.tokenStart);A(e,n|8192,16);let c=y2(e,n,t,u,o);return e.finishNode({type:"WhileStatement",test:l,body:c},i)}function y2(e,n,t,u,o){return C2(e,(n|33554432)^33554432|32768,t,u,0,{loop:1,$:o},0)}function Lt(e,n,t){(n&32768)===0&&f(e,68);let u=e.tokenStart;k(e,n);let o=null;if((e.flags&1)===0&&e.getToken()&143360){let{tokenValue:i}=e;o=N(e,n|8192),Xe(e,t,i,1)||f(e,138,i)}return U(e,n|8192),e.finishNode({type:"ContinueStatement",label:o},u)}function It(e,n,t){let u=e.tokenStart;k(e,n|8192);let o=null;if((e.flags&1)===0&&e.getToken()&143360){let{tokenValue:i}=e;o=N(e,n|8192),Xe(e,t,i,0)||f(e,138,i)}else(n&33792)===0&&f(e,69);return U(e,n|8192),e.finishNode({type:"BreakStatement",label:o},u)}function qt(e,n,t,u,o){let i=e.tokenStart;k(e,n),n&256&&f(e,91),A(e,n|8192,67174411);let l=V(e,n,u,0,1,e.tokenStart);A(e,n|8192,16);let c=C2(e,n,t,u,2,o,0);return e.finishNode({type:"WithStatement",object:l,body:c},i)}function Pt(e,n){let t=e.tokenStart;return k(e,n|8192),U(e,n|8192),e.finishNode({type:"DebuggerStatement"},t)}function vt(e,n,t,u,o){let i=e.tokenStart;k(e,n|8192);let l=t?q(t,32):void 0,c=A2(e,n,l,u,{$:o}),{tokenStart:d}=e,g=r(e,n|8192,20557)?Ot(e,n,t,u,o,d):null,a=null;if(e.getToken()===20566){k(e,n|8192);let s=l?q(t,4):void 0;a=A2(e,n,s,u,{$:o})}return!g&&!a&&f(e,88),e.finishNode({type:"TryStatement",block:c,handler:g,finalizer:a},i)}function Ot(e,n,t,u,o,i){let l=null,c=t;r(e,n,67174411)&&(t&&(t=q(t,4)),l=ln(e,n,t,u,(e.getToken()&2097152)===2097152?256:512,0),e.getToken()===18?f(e,86):e.getToken()===1077936155&&f(e,87),A(e,n|8192,16)),t&&(c=q(t,64));let d=A2(e,n,c,u,{$:o});return e.finishNode({type:"CatchClause",param:l,body:d},i)}function Rt(e,n,t,u,o){t&&(t=q(t,2));let i=1475584;return n=(n|i)^i|65536|524288|268435456|16777216,A2(e,n,t,u,{},o,"StaticBlock")}function Vt(e,n,t,u,o){let i=e.tokenStart;k(e,n|8192);let l=y2(e,n,t,u,o);A(e,n,20578),A(e,n|8192,67174411);let c=V(e,n,u,0,1,e.tokenStart);return A(e,n|8192,16),r(e,n|8192,1074790417),e.finishNode({type:"DoWhileStatement",body:l,test:c},i)}function Mt(e,n,t,u,o){let{tokenValue:i,tokenStart:l}=e,c=e.getToken(),d=N(e,n);if(e.getToken()&2240512){let g=d2(e,n,t,u,8,0);return U(e,n|8192),e.finishNode({type:"VariableDeclaration",kind:"let",declarations:g},l)}if(e.assignable=1,n&256&&f(e,85),e.getToken()===21)return ue(e,n,t,u,o,{},i,d,c,0,l);if(e.getToken()===10){let g;n&16&&(g=M2(e,n,i)),e.flags=(e.flags|128)^128,d=E2(e,n,g,u,[d],0,l)}else d=F(e,n,u,d,0,0,l),d=I(e,n,u,0,0,l,d);return e.getToken()===18&&(d=Q(e,n,u,0,l,d)),s2(e,n,d,l)}function Q2(e,n,t,u,o,i){let l=e.tokenStart;k(e,n);let c=d2(e,n,t,u,o,i);return U(e,n|8192),e.finishNode({type:"VariableDeclaration",kind:o&8?"let":"const",declarations:c},l)}function $e(e,n,t,u,o){let i=e.tokenStart;k(e,n);let l=d2(e,n,t,u,4,o);return U(e,n|8192),e.finishNode({type:"VariableDeclaration",kind:"var",declarations:l},i)}function d2(e,n,t,u,o,i){let l=1,c=[Se(e,n,t,u,o,i)];for(;r(e,n,18);)l++,c.push(Se(e,n,t,u,o,i));return l>1&&i&32&&e.getToken()&262144&&f(e,61,S[e.getToken()&255]),c}function Se(e,n,t,u,o,i){let{tokenStart:l}=e,c=e.getToken(),d=null,g=ln(e,n,t,u,o,i);return e.getToken()===1077936155?(k(e,n|8192),d=L(e,n,u,1,0,e.tokenStart),(i&32||(c&2097152)===0)&&(e.getToken()===471156||e.getToken()===8673330&&(c&2097152||(o&4)===0||n&256))&&J(l,e.currentLocation,60,e.getToken()===471156?"of":"in")):(o&16||(c&2097152)>0)&&(e.getToken()&262144)!==262144&&f(e,59,o&16?"const":"destructuring"),e.finishNode({type:"VariableDeclarator",id:g,init:d},l)}function Ut(e,n,t,u,o){let i=e.tokenStart;k(e,n);let l=((n&524288)>0||(n&512)>0&&(n&2048)>0)&&r(e,n,209006);A(e,n|8192,67174411),t&&(t=q(t,1));let c=null,d=null,g=0,a=null,s=e.getToken()===86088||e.getToken()===241737||e.getToken()===86090,h,{tokenStart:T}=e,C=e.getToken();if(s)C===241737?(a=N(e,n),e.getToken()&2240512?(e.getToken()===8673330?n&256&&f(e,67):a=e.finishNode({type:"VariableDeclaration",kind:"let",declarations:d2(e,n|33554432,t,u,8,32)},T),e.assignable=1):n&256?f(e,67):(s=!1,e.assignable=1,a=F(e,n,u,a,0,0,T),e.getToken()===471156&&f(e,115))):(k(e,n),a=e.finishNode(C===86088?{type:"VariableDeclaration",kind:"var",declarations:d2(e,n|33554432,t,u,4,32)}:{type:"VariableDeclaration",kind:"const",declarations:d2(e,n|33554432,t,u,16,32)},T),e.assignable=1);else if(C===1074790417)l&&f(e,82);else if((C&2097152)===2097152){let b=e.tokenStart;a=C===2162700?z(e,n,void 0,u,1,0,0,2,32):H(e,n,void 0,u,1,0,0,2,32),g=e.destructible,g&64&&f(e,63),e.assignable=g&16?2:1,a=F(e,n|33554432,u,a,0,0,b)}else a=X(e,n|33554432,u,1,0,1);if((e.getToken()&262144)===262144){if(e.getToken()===471156){e.assignable&2&&f(e,80,l?"await":"of"),$(e,a),k(e,n|8192),h=L(e,n,u,1,0,e.tokenStart),A(e,n|8192,16);let D=y2(e,n,t,u,o);return e.finishNode({type:"ForOfStatement",left:a,right:h,body:D,await:l},i)}e.assignable&2&&f(e,80,"in"),$(e,a),k(e,n|8192),l&&f(e,82),h=V(e,n,u,0,1,e.tokenStart),A(e,n|8192,16);let b=y2(e,n,t,u,o);return e.finishNode({type:"ForInStatement",body:b,left:a,right:h},i)}l&&f(e,82),s||(g&8&&e.getToken()!==1077936155&&f(e,80,"loop"),a=I(e,n|33554432,u,0,0,T,a)),e.getToken()===18&&(a=Q(e,n,u,0,T,a)),A(e,n|8192,1074790417),e.getToken()!==1074790417&&(c=V(e,n,u,0,1,e.tokenStart)),A(e,n|8192,1074790417),e.getToken()!==16&&(d=V(e,n,u,0,1,e.tokenStart)),A(e,n|8192,16);let E=y2(e,n,t,u,o);return e.finishNode({type:"ForStatement",init:a,test:c,update:d,body:E},i)}function We(e,n,t){return b2(n,e.getToken())||f(e,118),(e.getToken()&537079808)===537079808&&f(e,119),t&&n2(e,n,t,e.tokenValue,8,0),N(e,n)}function Jt(e,n,t){let u=e.tokenStart;k(e,n);let o=null,{tokenStart:i}=e,l=[];if(e.getToken()===134283267)o=v(e,n);else{if(e.getToken()&143360){let g=We(e,n,t);if(l=[e.finishNode({type:"ImportDefaultSpecifier",local:g},i)],r(e,n,18))switch(e.getToken()){case 8391476:l.push(Fe(e,n,t));break;case 2162700:Ne(e,n,t,l);break;default:f(e,107)}}else switch(e.getToken()){case 8391476:l=[Fe(e,n,t)];break;case 2162700:Ne(e,n,t,l);break;case 67174411:return Qe(e,n,void 0,u);case 67108877:return Ye(e,n,u);default:f(e,30,S[e.getToken()&255])}o=_t(e,n)}let c=Z2(e,n,l),d={type:"ImportDeclaration",specifiers:l,source:o,attributes:c};return U(e,n|8192),e.finishNode(d,u)}function Fe(e,n,t){let{tokenStart:u}=e;return k(e,n),A(e,n,77932),(e.getToken()&134217728)===134217728&&J(u,e.currentLocation,30,S[e.getToken()&255]),e.finishNode({type:"ImportNamespaceSpecifier",local:We(e,n,t)},u)}function _t(e,n){return A(e,n,209011),e.getToken()!==134283267&&f(e,105,"Import"),v(e,n)}function Ne(e,n,t,u){for(k(e,n);e.getToken()&143360||e.getToken()===134283267;){let{tokenValue:o,tokenStart:i}=e,l=e.getToken(),c=N2(e,n),d;r(e,n,77932)?((e.getToken()&134217728)===134217728||e.getToken()===18?f(e,106):P2(e,n,16,e.getToken(),0),o=e.tokenValue,d=N(e,n)):c.type==="Identifier"?(P2(e,n,16,l,0),d=c):f(e,25,S[108]),t&&n2(e,n,t,o,8,0),u.push(e.finishNode({type:"ImportSpecifier",local:d,imported:c},i)),e.getToken()!==1074790415&&A(e,n,18)}return A(e,n,1074790415),u}function Ye(e,n,t){let u=Ze(e,n,e.finishNode({type:"Identifier",name:"import"},t),t);return u=F(e,n,void 0,u,0,0,t),u=I(e,n,void 0,0,0,t,u),e.getToken()===18&&(u=Q(e,n,void 0,0,t,u)),s2(e,n,u,t)}function Qe(e,n,t,u){let o=Ge(e,n,t,0,u);return o=F(e,n,t,o,0,0,u),e.getToken()===18&&(o=Q(e,n,t,0,u,o)),s2(e,n,o,u)}function jt(e,n,t){let u=e.leadingDecorators.decorators.length?e.leadingDecorators.start:e.tokenStart;k(e,n|8192);let o=[],i=null,l=null,c=[];if(r(e,n|8192,20561)){switch(e.getToken()){case 86104:{i=e2(e,n,t,void 0,4,1,1,0,e.tokenStart);break}case 132:case 86094:i=G2(e,n,t,void 0,1);break;case 209005:{let{tokenStart:g}=e;i=N(e,n);let{flags:a}=e;(a&1)===0&&(e.getToken()===86104?i=e2(e,n,t,void 0,4,1,1,1,g):e.getToken()===67174411?(i=ae(e,n,void 0,i,1,1,0,a,g),i=F(e,n,void 0,i,0,0,g),i=I(e,n,void 0,0,0,g,i)):e.getToken()&143360&&(t&&(t=M2(e,n,e.tokenValue)),i=N(e,n),i=E2(e,n,t,void 0,[i],1,g)));break}default:i=L(e,n,void 0,1,0,e.tokenStart),U(e,n|8192)}return t&&t2(e,"default"),e.finishNode({type:"ExportDefaultDeclaration",declaration:i},u)}switch(e.getToken()){case 8391476:{k(e,n);let g=null;r(e,n,77932)&&(t&&t2(e,e.tokenValue),g=N2(e,n)),A(e,n,209011),e.getToken()!==134283267&&f(e,105,"Export"),l=v(e,n);let s=Z2(e,n),h={type:"ExportAllDeclaration",source:l,exported:g,attributes:s};return U(e,n|8192),e.finishNode(h,u)}case 2162700:{k(e,n);let g=[],a=[],s=0;for(;e.getToken()&143360||e.getToken()===134283267;){let{tokenStart:h,tokenValue:T}=e,C=N2(e,n);C.type==="Literal"&&(s=1);let E;e.getToken()===77932?(k(e,n),(e.getToken()&143360)===0&&e.getToken()!==134283267&&f(e,106),t&&(g.push(e.tokenValue),a.push(T)),E=N2(e,n)):(t&&(g.push(e.tokenValue),a.push(e.tokenValue)),E=C),o.push(e.finishNode({type:"ExportSpecifier",local:C,exported:E},h)),e.getToken()!==1074790415&&A(e,n,18)}A(e,n,1074790415),r(e,n,209011)?(e.getToken()!==134283267&&f(e,105,"Export"),l=v(e,n),c=Z2(e,n,o),t&&g.forEach(h=>t2(e,h))):(s&&f(e,172),t&&(g.forEach(h=>t2(e,h)),a.forEach(h=>ht(e,h)))),U(e,n|8192);break}case 132:case 86094:i=G2(e,n,t,void 0,2);break;case 86104:i=e2(e,n,t,void 0,4,1,2,0,e.tokenStart);break;case 241737:i=Q2(e,n,t,void 0,8,64);break;case 86090:i=Q2(e,n,t,void 0,16,64);break;case 86088:i=$e(e,n,t,void 0,64);break;case 209005:{let{tokenStart:g}=e;if(k(e,n),(e.flags&1)===0&&e.getToken()===86104){i=e2(e,n,t,void 0,4,1,2,1,g);break}}default:f(e,30,S[e.getToken()&255])}let d={type:"ExportNamedDeclaration",declaration:i,specifiers:o,source:l,attributes:c};return e.finishNode(d,u)}function L(e,n,t,u,o,i){let l=j(e,n,t,2,0,u,o,1,i);return l=F(e,n,t,l,o,0,i),I(e,n,t,o,0,i,l)}function Q(e,n,t,u,o,i){let l=[i];for(;r(e,n|8192,18);)l.push(L(e,n,t,1,u,e.tokenStart));return e.finishNode({type:"SequenceExpression",expressions:l},o)}function V(e,n,t,u,o,i){let l=L(e,n,t,o,u,i);return e.getToken()===18?Q(e,n,t,u,i,l):l}function I(e,n,t,u,o,i,l){let c=e.getToken();if((c&4194304)===4194304){e.assignable&2&&f(e,26),(!o&&c===1077936155&&l.type==="ArrayExpression"||l.type==="ObjectExpression")&&$(e,l),k(e,n|8192);let d=L(e,n,t,1,u,e.tokenStart);return e.assignable=2,e.finishNode(o?{type:"AssignmentPattern",left:l,right:d}:{type:"AssignmentExpression",left:l,operator:S[c&255],right:d},i)}return(c&8388608)===8388608&&(l=p(e,n,t,u,i,4,c,l)),r(e,n|8192,22)&&(l=u2(e,n,t,l,i)),l}function S2(e,n,t,u,o,i,l){let c=e.getToken();k(e,n|8192);let d=L(e,n,t,1,u,e.tokenStart);return l=e.finishNode(o?{type:"AssignmentPattern",left:l,right:d}:{type:"AssignmentExpression",left:l,operator:S[c&255],right:d},i),e.assignable=2,l}function u2(e,n,t,u,o){let i=L(e,(n|33554432)^33554432,t,1,0,e.tokenStart);A(e,n|8192,21),e.assignable=1;let l=L(e,n,t,1,0,e.tokenStart);return e.assignable=2,e.finishNode({type:"ConditionalExpression",test:u,consequent:i,alternate:l},o)}function p(e,n,t,u,o,i,l,c){let d=-((n&33554432)>0)&8673330,g,a;for(e.assignable=2;e.getToken()&8388608&&(g=e.getToken(),a=g&3840,(g&524288&&l&268435456||l&524288&&g&268435456)&&f(e,165),!(a+((g===8391735)<<8)-((d===g)<<12)<=i));)k(e,n|8192),c=e.finishNode({type:g&524288||g&268435456?"LogicalExpression":"BinaryExpression",left:c,right:p(e,n,t,u,e.tokenStart,a,g,X(e,n,t,0,u,1)),operator:S[g&255]},o);return e.getToken()===1077936155&&f(e,26),c}function Xt(e,n,t,u,o){u||f(e,0);let{tokenStart:i}=e,l=e.getToken();k(e,n|8192);let c=X(e,n,t,0,o,1);return e.getToken()===8391735&&f(e,33),n&256&&l===16863276&&(c.type==="Identifier"?f(e,121):dt(c)&&f(e,127)),e.assignable=2,e.finishNode({type:"UnaryExpression",operator:S[l&255],argument:c,prefix:!0},i)}function Ht(e,n,t,u,o,i,l,c){let d=e.getToken(),g=N(e,n),{flags:a}=e;if((a&1)===0){if(e.getToken()===86104)return pe(e,n,t,1,u,c);if(b2(n,e.getToken()))return o||f(e,0),(e.getToken()&36864)===36864&&(e.flags|=256),tn(e,n,t,i,c)}return!l&&e.getToken()===67174411?ae(e,n,t,g,i,1,0,a,c):e.getToken()===10?(U2(e,n,d),l&&f(e,51),(d&36864)===36864&&(e.flags|=256),_2(e,n,t,e.tokenValue,g,l,i,0,c)):(e.assignable=1,g)}function zt(e,n,t,u,o,i){if(u&&(e.destructible|=256),n&262144){k(e,n|8192),n&2097152&&f(e,32),o||f(e,26),e.getToken()===22&&f(e,124);let l=null,c=!1;return(e.flags&1)===0?(c=r(e,n|8192,8391476),(e.getToken()&77824||c)&&(l=L(e,n,t,1,0,e.tokenStart))):e.getToken()===8391476&&f(e,30,S[e.getToken()&255]),e.assignable=2,e.finishNode({type:"YieldExpression",argument:l,delegate:c},i)}return n&256&&f(e,97,"yield"),de(e,n,t)}function Kt(e,n,t,u,o,i){o&&(e.destructible|=128),n&268435456&&f(e,177);let l=de(e,n,t);if(l.type==="ArrowFunctionExpression"||(e.getToken()&65536)===0)return n&524288&&J(i,{index:e.startIndex,line:e.startLine,column:e.startColumn},176),n&512&&J(i,{index:e.startIndex,line:e.startLine,column:e.startColumn},110),n&2097152&&n&524288&&J(i,{index:e.startIndex,line:e.startLine,column:e.startColumn},110),l;if(n&2097152&&J(i,{index:e.startIndex,line:e.startLine,column:e.startColumn},31),n&524288||n&512&&n&2048){u&&J(i,{index:e.startIndex,line:e.startLine,column:e.startColumn},0);let d=X(e,n,t,0,0,1);return e.getToken()===8391735&&f(e,33),e.assignable=2,e.finishNode({type:"AwaitExpression",argument:d},i)}return n&512&&J(i,{index:e.startIndex,line:e.startLine,column:e.startColumn},98),l}function J2(e,n,t,u,o,i,l){let{tokenStart:c}=e;A(e,n|8192,2162700);let d=[];if(e.getToken()!==1074790415){for(;e.getToken()===134283267;){let{index:g,tokenStart:a,tokenIndex:s,tokenValue:h}=e,T=e.getToken(),C=v(e,n);Je(e,g,s,h)&&(n|=256,e.flags&128&&J(a,e.currentLocation,66),e.flags&64&&J(a,e.currentLocation,9),e.flags&4096&&J(a,e.currentLocation,15),l&&V2(l)),d.push(ie(e,n,C,T,a))}n&256&&(i&&((i&537079808)===537079808&&f(e,119),(i&36864)===36864&&f(e,40)),e.flags&512&&f(e,119),e.flags&256&&f(e,118))}for(e.flags=(e.flags|512|256|64|4096)^4928,e.destructible=(e.destructible|256)^256;e.getToken()!==1074790415;)d.push(D2(e,n,t,u,4,{}));return A(e,o&24?n|8192:n,1074790415),e.flags&=-4289,e.getToken()===1077936155&&f(e,26),e.finishNode({type:"BlockStatement",body:d},c)}function $t(e,n){let{tokenStart:t}=e;switch(k(e,n),e.getToken()){case 67108990:f(e,167);case 67174411:{(n&131072)===0&&f(e,28),e.assignable=2;break}case 69271571:case 67108877:{(n&65536)===0&&f(e,29),e.assignable=1;break}default:f(e,30,"super")}return e.finishNode({type:"Super"},t)}function X(e,n,t,u,o,i){let l=e.tokenStart,c=j(e,n,t,2,0,u,o,i,l);return F(e,n,t,c,o,0,l)}function Wt(e,n,t,u){e.assignable&2&&f(e,55);let o=e.getToken();return k(e,n),e.assignable=2,e.finishNode({type:"UpdateExpression",argument:t,operator:S[o&255],prefix:!1},u)}function F(e,n,t,u,o,i,l){if((e.getToken()&33619968)===33619968&&(e.flags&1)===0)u=Wt(e,n,u,l);else if((e.getToken()&67108864)===67108864){switch(n=(n|33554432)^33554432,e.getToken()){case 67108877:{k(e,(n|67108864|2048)^2048),n&4096&&e.getToken()===130&&e.tokenValue==="super"&&f(e,173),e.assignable=1;let c=oe(e,n|16384,t);u=e.finishNode({type:"MemberExpression",object:u,computed:!1,property:c,optional:!1},l);break}case 69271571:{let c=!1;(e.flags&2048)===2048&&(c=!0,e.flags=(e.flags|2048)^2048),k(e,n|8192);let{tokenStart:d}=e,g=V(e,n,t,o,1,d);A(e,n,20),e.assignable=1,u=e.finishNode({type:"MemberExpression",object:u,computed:!0,property:g,optional:!1},l),c&&(e.flags|=2048);break}case 67174411:{if((e.flags&1024)===1024)return e.flags=(e.flags|1024)^1024,u;let c=!1;(e.flags&2048)===2048&&(c=!0,e.flags=(e.flags|2048)^2048);let d=ce(e,n,t,o);e.assignable=2,u=e.finishNode({type:"CallExpression",callee:u,arguments:d,optional:!1},l),c&&(e.flags|=2048);break}case 67108990:{k(e,(n|67108864|2048)^2048),e.flags|=2048,e.assignable=2,u=Yt(e,n,t,u,l);break}default:(e.flags&2048)===2048&&f(e,166),e.assignable=2,u=e.finishNode({type:"TaggedTemplateExpression",tag:u,quasi:e.getToken()===67174408?fe(e,n|16384,t):le(e,n)},l)}u=F(e,n,t,u,0,1,l)}return i===0&&(e.flags&2048)===2048&&(e.flags=(e.flags|2048)^2048,u=e.finishNode({type:"ChainExpression",expression:u},l)),u}function Yt(e,n,t,u,o){let i=!1,l;if((e.getToken()===69271571||e.getToken()===67174411)&&(e.flags&2048)===2048&&(i=!0,e.flags=(e.flags|2048)^2048),e.getToken()===69271571){k(e,n|8192);let{tokenStart:c}=e,d=V(e,n,t,0,1,c);A(e,n,20),e.assignable=2,l=e.finishNode({type:"MemberExpression",object:u,computed:!0,optional:!0,property:d},o)}else if(e.getToken()===67174411){let c=ce(e,n,t,0);e.assignable=2,l=e.finishNode({type:"CallExpression",callee:u,arguments:c,optional:!0},o)}else{let c=oe(e,n,t);e.assignable=2,l=e.finishNode({type:"MemberExpression",object:u,computed:!1,optional:!0,property:c},o)}return i&&(e.flags|=2048),l}function oe(e,n,t){return(e.getToken()&143360)===0&&e.getToken()!==-2147483528&&e.getToken()!==-2147483527&&e.getToken()!==130&&f(e,160),e.getToken()===130?R2(e,n,t,0):N(e,n)}function Qt(e,n,t,u,o,i){u&&f(e,56),o||f(e,0);let l=e.getToken();k(e,n|8192);let c=X(e,n,t,0,0,1);return e.assignable&2&&f(e,55),e.assignable=2,e.finishNode({type:"UpdateExpression",argument:c,operator:S[l&255],prefix:!0},i)}function j(e,n,t,u,o,i,l,c,d){if((e.getToken()&143360)===143360){switch(e.getToken()){case 209006:return Kt(e,n,t,o,l,d);case 241771:return zt(e,n,t,l,i,d);case 209005:return Ht(e,n,t,l,c,i,o,d)}let{tokenValue:g}=e,a=e.getToken(),s=N(e,n|16384);return e.getToken()===10?(c||f(e,0),U2(e,n,a),(a&36864)===36864&&(e.flags|=256),_2(e,n,t,g,s,o,i,0,d)):(n&4096&&!(n&8388608)&&!(n&2097152)&&e.tokenValue==="arguments"&&f(e,130),(a&255)===73&&(n&256&&f(e,113),u&24&&f(e,100)),e.assignable=n&256&&(a&537079808)===537079808?2:1,s)}if((e.getToken()&134217728)===134217728)return v(e,n);switch(e.getToken()){case 33619993:case 33619994:return Qt(e,n,t,o,c,d);case 16863276:case 16842798:case 16842799:case 25233968:case 25233969:case 16863275:case 16863277:return Xt(e,n,t,c,l);case 86104:return pe(e,n,t,0,l,d);case 2162700:return iu(e,n,t,i?0:1,l);case 69271571:return uu(e,n,t,i?0:1,l);case 67174411:return lu(e,n|16384,t,i,1,0,d);case 86021:case 86022:case 86023:return nu(e,n);case 86111:return tu(e,n);case 65540:return du(e,n);case 132:case 86094:return au(e,n,t,l,d);case 86109:return $t(e,n);case 67174409:return le(e,n);case 67174408:return fe(e,n,t);case 86107:return fu(e,n,t,l);case 134283388:return xe(e,n);case 130:return R2(e,n,t,0);case 86106:return Zt(e,n,t,o,l,d);case 8456256:if(n&8)return X2(e,n,t,0,e.tokenStart);default:if(b2(n,e.getToken()))return de(e,n,t);f(e,30,S[e.getToken()&255])}}function Zt(e,n,t,u,o,i){let l=N(e,n);return e.getToken()===67108877?Ze(e,n,l,i):(u&&f(e,142),l=Ge(e,n,t,o,i),e.assignable=2,F(e,n,t,l,o,0,i))}function Ze(e,n,t,u){(n&512)===0&&f(e,169),k(e,n);let o=e.getToken();return o!==209030&&e.tokenValue!=="meta"?f(e,174):o&-2147483648&&f(e,175),e.assignable=2,e.finishNode({type:"MetaProperty",meta:t,property:N(e,n)},u)}function Ge(e,n,t,u,o){A(e,n|8192,67174411),e.getToken()===14&&f(e,143);let i=L(e,n,t,1,u,e.tokenStart),l=null;if(e.getToken()===18){if(A(e,n,18),e.getToken()!==16){let d=(n|33554432)^33554432;l=L(e,d,t,1,u,e.tokenStart)}r(e,n,18)}let c={type:"ImportExpression",source:i,options:l};return A(e,n,16),e.finishNode(c,o)}function Z2(e,n,t=null){if(!r(e,n,20579))return[];A(e,n,2162700);let u=[],o=new Set;for(;e.getToken()!==1074790415;){let i=e.tokenStart,l=xt(e,n);A(e,n,21);let c=Gt(e,n),d=l.type==="Literal"?l.value:l.name;d==="type"&&c.value==="json"&&(t===null||t.length===1&&(t[0].type==="ImportDefaultSpecifier"||t[0].type==="ImportNamespaceSpecifier"||t[0].type==="ImportSpecifier"&&t[0].imported.type==="Identifier"&&t[0].imported.name==="default"||t[0].type==="ExportSpecifier"&&t[0].local.type==="Identifier"&&t[0].local.name==="default")||f(e,140)),o.has(d)&&f(e,145,`${d}`),o.add(d),u.push(e.finishNode({type:"ImportAttribute",key:l,value:c},i)),e.getToken()!==1074790415&&A(e,n,18)}return A(e,n,1074790415),u}function Gt(e,n){if(e.getToken()===134283267)return v(e,n);f(e,30,S[e.getToken()&255])}function xt(e,n){if(e.getToken()===134283267)return v(e,n);if(e.getToken()&143360)return N(e,n);f(e,30,S[e.getToken()&255])}function pt(e,n){let t=n.length;for(let u=0;u<t;u++){let o=n.charCodeAt(u);(o&64512)===55296&&(o>56319||++u>=t||(n.charCodeAt(u)&64512)!==56320)&&f(e,171,JSON.stringify(n.charAt(u--)))}}function N2(e,n){if(e.getToken()===134283267)return pt(e,e.tokenValue),v(e,n);if(e.getToken()&143360)return N(e,n);f(e,30,S[e.getToken()&255])}function xe(e,n){let{tokenRaw:t,tokenValue:u,tokenStart:o}=e;k(e,n),e.assignable=2;let i={type:"Literal",value:u,bigint:String(u)};return n&128&&(i.raw=t),e.finishNode(i,o)}function le(e,n){e.assignable=2;let{tokenValue:t,tokenRaw:u,tokenStart:o}=e;A(e,n,67174409);let i=[L2(e,n,t,u,o,!0)];return e.finishNode({type:"TemplateLiteral",expressions:[],quasis:i},o)}function fe(e,n,t){n=(n|33554432)^33554432;let{tokenValue:u,tokenRaw:o,tokenStart:i}=e;A(e,n&-16385|8192,67174408);let l=[L2(e,n,u,o,i,!1)],c=[V(e,n&-16385,t,0,1,e.tokenStart)];for(e.getToken()!==1074790415&&f(e,83);e.setToken(tt(e,n),!0)!==67174409;){let{tokenValue:d,tokenRaw:g,tokenStart:a}=e;A(e,n&-16385|8192,67174408),l.push(L2(e,n,d,g,a,!1)),c.push(V(e,n,t,0,1,e.tokenStart)),e.getToken()!==1074790415&&f(e,83)}{let{tokenValue:d,tokenRaw:g,tokenStart:a}=e;A(e,n,67174409),l.push(L2(e,n,d,g,a,!0))}return e.finishNode({type:"TemplateLiteral",expressions:c,quasis:l},i)}function L2(e,n,t,u,o,i){let l=e.finishNode({type:"TemplateElement",value:{cooked:t,raw:u},tail:i},o),c=i?1:2;return n&2&&(l.start+=1,l.range[0]+=1,l.end-=c,l.range[1]-=c),n&4&&(l.loc.start.column+=1,l.loc.end.column-=c),l}function eu(e,n,t){let u=e.tokenStart;n=(n|33554432)^33554432,A(e,n|8192,14);let o=L(e,n,t,1,0,e.tokenStart);return e.assignable=1,e.finishNode({type:"SpreadElement",argument:o},u)}function ce(e,n,t,u){k(e,n|8192);let o=[];if(e.getToken()===16)return k(e,n|16384),o;for(;e.getToken()!==16&&(e.getToken()===14?o.push(eu(e,n,t)):o.push(L(e,n,t,1,u,e.tokenStart)),!(e.getToken()!==18||(k(e,n|8192),e.getToken()===16))););return A(e,n|16384,16),o}function N(e,n){let{tokenValue:t,tokenStart:u}=e,o=t==="await"&&(e.getToken()&-2147483648)===0;return k(e,n|(o?8192:0)),e.finishNode({type:"Identifier",name:t},u)}function v(e,n){let{tokenValue:t,tokenRaw:u,tokenStart:o}=e;return e.getToken()===134283388?xe(e,n):(k(e,n),e.assignable=2,e.finishNode(n&128?{type:"Literal",value:t,raw:u}:{type:"Literal",value:t},o))}function nu(e,n){let t=e.tokenStart,u=S[e.getToken()&255],o=e.getToken()===86023?null:u==="true";return k(e,n),e.assignable=2,e.finishNode(n&128?{type:"Literal",value:o,raw:u}:{type:"Literal",value:o},t)}function tu(e,n){let{tokenStart:t}=e;return k(e,n),e.assignable=2,e.finishNode({type:"ThisExpression"},t)}function e2(e,n,t,u,o,i,l,c,d){k(e,n|8192);let g=i?te(e,n,8391476):0,a=null,s,h=t?l2():void 0;if(e.getToken()===67174411)(l&1)===0&&f(e,39,"Function");else{let b=o&4&&((n&2048)===0||(n&512)===0)?4:64|(c?1024:0)|(g?1024:0);_e(e,n,e.getToken()),t&&(b&4?He(e,n,t,e.tokenValue,b):n2(e,n,t,e.tokenValue,b,o),h=q(h,256),l&&l&2&&t2(e,e.tokenValue)),s=e.getToken(),e.getToken()&143360?a=N(e,n):f(e,30,S[e.getToken()&255])}n=(n|7274496)^7274496|16777216|(c?524288:0)|(g?262144:0)|(g?0:67108864),t&&(h=q(h,512));let T=nn(e,(n|2097152)&-268435457,h,u,0,1),C=268471296,E=J2(e,(n|C)^C|8388608|1048576,t?q(h,128):h,u,8,s,h==null?void 0:h.scopeError);return e.finishNode({type:"FunctionDeclaration",id:a,params:T,body:E,async:c===1,generator:g===1},d)}function pe(e,n,t,u,o,i){k(e,n|8192);let l=te(e,n,8391476),c=(u?524288:0)|(l?262144:0),d=null,g,a=n&16?l2():void 0,s=275709952;e.getToken()&143360&&(_e(e,(n|s)^s|c,e.getToken()),a&&(a=q(a,256)),g=e.getToken(),d=N(e,n)),n=(n|s)^s|16777216|c|(l?0:67108864),a&&(a=q(a,512));let h=nn(e,(n|2097152)&-268435457,a,t,o,1),T=J2(e,n&-33594369|8388608|1048576,a&&q(a,128),t,0,g,a==null?void 0:a.scopeError);return e.assignable=2,e.finishNode({type:"FunctionExpression",id:d,params:h,body:T,async:u===1,generator:l===1},i)}function uu(e,n,t,u,o){let i=H(e,n,void 0,t,u,o,0,2,0);return e.destructible&64&&f(e,63),e.destructible&8&&f(e,62),i}function H(e,n,t,u,o,i,l,c,d){let{tokenStart:g}=e;k(e,n|8192);let a=[],s=0;for(n=(n|33554432)^33554432;e.getToken()!==20;)if(r(e,n|8192,18))a.push(null);else{let T,{tokenStart:C,tokenValue:E}=e,b=e.getToken();if(b&143360)if(T=j(e,n,u,c,0,1,i,1,C),e.getToken()===1077936155){e.assignable&2&&f(e,26),k(e,n|8192),t&&Y(e,n,t,E,c,d);let D=L(e,n,u,1,i,e.tokenStart);T=e.finishNode(l?{type:"AssignmentPattern",left:T,right:D}:{type:"AssignmentExpression",operator:"=",left:T,right:D},C),s|=e.destructible&256?256:0|e.destructible&128?128:0}else e.getToken()===18||e.getToken()===20?(e.assignable&2?s|=16:t&&Y(e,n,t,E,c,d),s|=e.destructible&256?256:0|e.destructible&128?128:0):(s|=c&1?32:(c&2)===0?16:0,T=F(e,n,u,T,i,0,C),e.getToken()!==18&&e.getToken()!==20?(e.getToken()!==1077936155&&(s|=16),T=I(e,n,u,i,l,C,T)):e.getToken()!==1077936155&&(s|=e.assignable&2?16:32));else b&2097152?(T=e.getToken()===2162700?z(e,n,t,u,0,i,l,c,d):H(e,n,t,u,0,i,l,c,d),s|=e.destructible,e.assignable=e.destructible&16?2:1,e.getToken()===18||e.getToken()===20?e.assignable&2&&(s|=16):e.destructible&8?f(e,71):(T=F(e,n,u,T,i,0,C),s=e.assignable&2?16:0,e.getToken()!==18&&e.getToken()!==20?T=I(e,n,u,i,l,C,T):e.getToken()!==1077936155&&(s|=e.assignable&2?16:32))):b===14?(T=g2(e,n,t,u,20,c,d,0,i,l),s|=e.destructible,e.getToken()!==18&&e.getToken()!==20&&f(e,30,S[e.getToken()&255])):(T=X(e,n,u,1,0,1),e.getToken()!==18&&e.getToken()!==20?(T=I(e,n,u,i,l,C,T),(c&3)===0&&b===67174411&&(s|=16)):e.assignable&2?s|=16:b===67174411&&(s|=e.assignable&1&&c&3?32:16));if(a.push(T),r(e,n|8192,18)){if(e.getToken()===20)break}else break}A(e,n,20);let h=e.finishNode({type:l?"ArrayPattern":"ArrayExpression",elements:a},g);return!o&&e.getToken()&4194304?en(e,n,u,s,i,l,g,h):(e.destructible=s,h)}function en(e,n,t,u,o,i,l,c){e.getToken()!==1077936155&&f(e,26),k(e,n|8192),u&16&&f(e,26),i||$(e,c);let{tokenStart:d}=e,g=L(e,n,t,1,o,d);return e.destructible=(u|64|8)^72|(e.destructible&128?128:0)|(e.destructible&256?256:0),e.finishNode(i?{type:"AssignmentPattern",left:c,right:g}:{type:"AssignmentExpression",left:c,operator:"=",right:g},l)}function g2(e,n,t,u,o,i,l,c,d,g){let{tokenStart:a}=e;k(e,n|8192);let s=null,h=0,{tokenValue:T,tokenStart:C}=e,E=e.getToken();if(E&143360)e.assignable=1,s=j(e,n,u,i,0,1,d,1,C),E=e.getToken(),s=F(e,n,u,s,d,0,C),e.getToken()!==18&&e.getToken()!==o&&(e.assignable&2&&e.getToken()===1077936155&&f(e,71),h|=16,s=I(e,n,u,d,g,C,s)),e.assignable&2?h|=16:E===o||E===18?t&&Y(e,n,t,T,i,l):h|=32,h|=e.destructible&128?128:0;else if(E===o)f(e,41);else if(E&2097152)s=e.getToken()===2162700?z(e,n,t,u,1,d,g,i,l):H(e,n,t,u,1,d,g,i,l),E=e.getToken(),E!==1077936155&&E!==o&&E!==18?(e.destructible&8&&f(e,71),s=F(e,n,u,s,d,0,C),h|=e.assignable&2?16:0,(e.getToken()&4194304)===4194304?(e.getToken()!==1077936155&&(h|=16),s=I(e,n,u,d,g,C,s)):((e.getToken()&8388608)===8388608&&(s=p(e,n,u,1,C,4,E,s)),r(e,n|8192,22)&&(s=u2(e,n,u,s,C)),h|=e.assignable&2?16:32)):h|=o===1074790415&&E!==1077936155?16:e.destructible;else{h|=32,s=X(e,n,u,1,d,1);let{tokenStart:b}=e,D=e.getToken();return D===1077936155?(e.assignable&2&&f(e,26),s=I(e,n,u,d,g,b,s),h|=16):(D===18?h|=16:D!==o&&(s=I(e,n,u,d,g,b,s)),h|=e.assignable&1?32:16),e.destructible=h,e.getToken()!==o&&e.getToken()!==18&&f(e,161),e.finishNode({type:g?"RestElement":"SpreadElement",argument:s},a)}if(e.getToken()!==o)if(i&1&&(h|=c?16:32),r(e,n|8192,1077936155)){h&16&&f(e,26),$(e,s);let b=L(e,n,u,1,d,e.tokenStart);s=e.finishNode(g?{type:"AssignmentPattern",left:s,right:b}:{type:"AssignmentExpression",left:s,operator:"=",right:b},C),h=16}else h|=16;return e.destructible=h,e.finishNode({type:g?"RestElement":"SpreadElement",argument:s},a)}function K(e,n,t,u,o,i){var a;let l=2883584|((u&64)===0?4325376:0);n=(n|l)^l|(u&8?262144:0)|(u&16?524288:0)|(u&64?4194304:0)|65536|8388608|16777216;let c=n&16?q(l2(),512):void 0,d=ou(e,(n|2097152)&-268435457,c,t,u,1,o);c&&(c=q(c,128));let g=J2(e,n&-301992961|8388608|1048576,c,t,0,void 0,(a=c==null?void 0:c.parent)==null?void 0:a.scopeError);return e.finishNode({type:"FunctionExpression",params:d,body:g,async:(u&16)>0,generator:(u&8)>0,id:null},i)}function iu(e,n,t,u,o){let i=z(e,n,void 0,t,u,o,0,2,0);return e.destructible&64&&f(e,63),e.destructible&8&&f(e,62),i}function z(e,n,t,u,o,i,l,c,d){let{tokenStart:g}=e;k(e,n);let a=[],s=0,h=0;for(n=(n|33554432)^33554432;e.getToken()!==1074790415;){let{tokenValue:C,tokenStart:E}=e,b=e.getToken();if(b===14)a.push(g2(e,n,t,u,1074790415,c,d,0,i,l));else{let D=0,R=null,y;if(e.getToken()&143360||e.getToken()===-2147483528||e.getToken()===-2147483527)if(e.getToken()===-2147483527&&(s|=16),R=N(e,n),e.getToken()===18||e.getToken()===1074790415||e.getToken()===1077936155)if(D|=4,n&256&&(b&537079808)===537079808?s|=16:P2(e,n,c,b,0),t&&Y(e,n,t,C,c,d),r(e,n|8192,1077936155)){s|=8;let w=L(e,n,u,1,i,e.tokenStart);s|=e.destructible&256?256:0|e.destructible&128?128:0,y=e.finishNode({type:"AssignmentPattern",left:n&134217728?Object.assign({},R):R,right:w},E)}else s|=(b===209006?128:0)|(b===-2147483528?16:0),y=n&134217728?Object.assign({},R):R;else if(r(e,n|8192,21)){let{tokenStart:w}=e;if(C==="__proto__"&&h++,e.getToken()&143360){let k2=e.getToken(),Z=e.tokenValue;y=j(e,n,u,c,0,1,i,1,w);let W=e.getToken();y=F(e,n,u,y,i,0,w),e.getToken()===18||e.getToken()===1074790415?W===1077936155||W===1074790415||W===18?(s|=e.destructible&128?128:0,e.assignable&2?s|=16:t&&(k2&143360)===143360&&Y(e,n,t,Z,c,d)):s|=e.assignable&1?32:16:(e.getToken()&4194304)===4194304?(e.assignable&2?s|=16:W!==1077936155?s|=32:t&&Y(e,n,t,Z,c,d),y=I(e,n,u,i,l,w,y)):(s|=16,(e.getToken()&8388608)===8388608&&(y=p(e,n,u,1,w,4,W,y)),r(e,n|8192,22)&&(y=u2(e,n,u,y,w)))}else(e.getToken()&2097152)===2097152?(y=e.getToken()===69271571?H(e,n,t,u,0,i,l,c,d):z(e,n,t,u,0,i,l,c,d),s=e.destructible,e.assignable=s&16?2:1,e.getToken()===18||e.getToken()===1074790415?e.assignable&2&&(s|=16):e.destructible&8?f(e,71):(y=F(e,n,u,y,i,0,w),s=e.assignable&2?16:0,(e.getToken()&4194304)===4194304?y=S2(e,n,u,i,l,w,y):((e.getToken()&8388608)===8388608&&(y=p(e,n,u,1,w,4,b,y)),r(e,n|8192,22)&&(y=u2(e,n,u,y,w)),s|=e.assignable&2?16:32))):(y=X(e,n,u,1,i,1),s|=e.assignable&1?32:16,e.getToken()===18||e.getToken()===1074790415?e.assignable&2&&(s|=16):(y=F(e,n,u,y,i,0,w),s=e.assignable&2?16:0,e.getToken()!==18&&b!==1074790415&&(e.getToken()!==1077936155&&(s|=16),y=I(e,n,u,i,l,w,y))))}else e.getToken()===69271571?(s|=16,b===209005&&(D|=16),D|=(b===209008?256:b===209009?512:1)|2,R=c2(e,n,u,i),s|=e.assignable,y=K(e,n,u,D,i,e.tokenStart)):e.getToken()&143360?(s|=16,b===-2147483528&&f(e,95),b===209005?(e.flags&1&&f(e,132),D|=17):b===209008?D|=256:b===209009?D|=512:f(e,0),R=N(e,n),y=K(e,n,u,D,i,e.tokenStart)):e.getToken()===67174411?(s|=16,D|=1,y=K(e,n,u,D,i,e.tokenStart)):e.getToken()===8391476?(s|=16,b===209008?f(e,42):b===209009?f(e,43):b!==209005&&f(e,30,S[52]),k(e,n),D|=9|(b===209005?16:0),e.getToken()&143360?R=N(e,n):(e.getToken()&134217728)===134217728?R=v(e,n):e.getToken()===69271571?(D|=2,R=c2(e,n,u,i),s|=e.assignable):f(e,30,S[e.getToken()&255]),y=K(e,n,u,D,i,e.tokenStart)):(e.getToken()&134217728)===134217728?(b===209005&&(D|=16),D|=b===209008?256:b===209009?512:1,s|=16,R=v(e,n),y=K(e,n,u,D,i,e.tokenStart)):f(e,133);else if((e.getToken()&134217728)===134217728)if(R=v(e,n),e.getToken()===21){A(e,n|8192,21);let{tokenStart:w}=e;if(C==="__proto__"&&h++,e.getToken()&143360){y=j(e,n,u,c,0,1,i,1,w);let{tokenValue:k2}=e,Z=e.getToken();y=F(e,n,u,y,i,0,w),e.getToken()===18||e.getToken()===1074790415?Z===1077936155||Z===1074790415||Z===18?e.assignable&2?s|=16:t&&Y(e,n,t,k2,c,d):s|=e.assignable&1?32:16:e.getToken()===1077936155?(e.assignable&2&&(s|=16),y=I(e,n,u,i,l,w,y)):(s|=16,y=I(e,n,u,i,l,w,y))}else(e.getToken()&2097152)===2097152?(y=e.getToken()===69271571?H(e,n,t,u,0,i,l,c,d):z(e,n,t,u,0,i,l,c,d),s=e.destructible,e.assignable=s&16?2:1,e.getToken()===18||e.getToken()===1074790415?e.assignable&2&&(s|=16):(e.destructible&8)!==8&&(y=F(e,n,u,y,i,0,w),s=e.assignable&2?16:0,(e.getToken()&4194304)===4194304?y=S2(e,n,u,i,l,w,y):((e.getToken()&8388608)===8388608&&(y=p(e,n,u,1,w,4,b,y)),r(e,n|8192,22)&&(y=u2(e,n,u,y,w)),s|=e.assignable&2?16:32))):(y=X(e,n,u,1,0,1),s|=e.assignable&1?32:16,e.getToken()===18||e.getToken()===1074790415?e.assignable&2&&(s|=16):(y=F(e,n,u,y,i,0,w),s=e.assignable&1?0:16,e.getToken()!==18&&e.getToken()!==1074790415&&(e.getToken()!==1077936155&&(s|=16),y=I(e,n,u,i,l,w,y))))}else e.getToken()===67174411?(D|=1,y=K(e,n,u,D,i,e.tokenStart),s=e.assignable|16):f(e,134);else if(e.getToken()===69271571)if(R=c2(e,n,u,i),s|=e.destructible&256?256:0,D|=2,e.getToken()===21){k(e,n|8192);let{tokenStart:w,tokenValue:k2}=e,Z=e.getToken();if(e.getToken()&143360){y=j(e,n,u,c,0,1,i,1,w);let W=e.getToken();y=F(e,n,u,y,i,0,w),(e.getToken()&4194304)===4194304?(s|=e.assignable&2?16:W===1077936155?0:32,y=S2(e,n,u,i,l,w,y)):e.getToken()===18||e.getToken()===1074790415?W===1077936155||W===1074790415||W===18?e.assignable&2?s|=16:t&&(Z&143360)===143360&&Y(e,n,t,k2,c,d):s|=e.assignable&1?32:16:(s|=16,y=I(e,n,u,i,l,w,y))}else(e.getToken()&2097152)===2097152?(y=e.getToken()===69271571?H(e,n,t,u,0,i,l,c,d):z(e,n,t,u,0,i,l,c,d),s=e.destructible,e.assignable=s&16?2:1,e.getToken()===18||e.getToken()===1074790415?e.assignable&2&&(s|=16):s&8?f(e,62):(y=F(e,n,u,y,i,0,w),s=e.assignable&2?s|16:0,(e.getToken()&4194304)===4194304?(e.getToken()!==1077936155&&(s|=16),y=S2(e,n,u,i,l,w,y)):((e.getToken()&8388608)===8388608&&(y=p(e,n,u,1,w,4,b,y)),r(e,n|8192,22)&&(y=u2(e,n,u,y,w)),s|=e.assignable&2?16:32))):(y=X(e,n,u,1,0,1),s|=e.assignable&1?32:16,e.getToken()===18||e.getToken()===1074790415?e.assignable&2&&(s|=16):(y=F(e,n,u,y,i,0,w),s=e.assignable&1?0:16,e.getToken()!==18&&e.getToken()!==1074790415&&(e.getToken()!==1077936155&&(s|=16),y=I(e,n,u,i,l,w,y))))}else e.getToken()===67174411?(D|=1,y=K(e,n,u,D,i,e.tokenStart),s=16):f(e,44);else if(b===8391476)if(A(e,n|8192,8391476),D|=8,e.getToken()&143360){let w=e.getToken();R=N(e,n),D|=1,e.getToken()===67174411?(s|=16,y=K(e,n,u,D,i,e.tokenStart)):J(e.tokenStart,e.currentLocation,w===209005?46:w===209008||e.getToken()===209009?45:47,S[w&255])}else(e.getToken()&134217728)===134217728?(s|=16,R=v(e,n),D|=1,y=K(e,n,u,D,i,e.tokenStart)):e.getToken()===69271571?(s|=16,D|=3,R=c2(e,n,u,i),y=K(e,n,u,D,i,e.tokenStart)):f(e,126);else f(e,30,S[b&255]);s|=e.destructible&128?128:0,e.destructible=s,a.push(e.finishNode({type:"Property",key:R,value:y,kind:D&768?D&512?"set":"get":"init",computed:(D&2)>0,method:(D&1)>0,shorthand:(D&4)>0},E))}if(s|=e.destructible,e.getToken()!==18)break;k(e,n)}A(e,n,1074790415),h>1&&(s|=64);let T=e.finishNode({type:l?"ObjectPattern":"ObjectExpression",properties:a},g);return!o&&e.getToken()&4194304?en(e,n,u,s,i,l,g,T):(e.destructible=s,T)}function ou(e,n,t,u,o,i,l){A(e,n,67174411);let c=[];if(e.flags=(e.flags|128)^128,e.getToken()===16)return o&512&&f(e,37,"Setter","one",""),k(e,n),c;o&256&&f(e,37,"Getter","no","s"),o&512&&e.getToken()===14&&f(e,38),n=(n|33554432)^33554432;let d=0,g=0;for(;e.getToken()!==18;){let a=null,{tokenStart:s}=e;if(e.getToken()&143360?((n&256)===0&&((e.getToken()&36864)===36864&&(e.flags|=256),(e.getToken()&537079808)===537079808&&(e.flags|=512)),a=se(e,n,t,o|1,0)):(e.getToken()===2162700?a=z(e,n,t,u,1,l,1,i,0):e.getToken()===69271571?a=H(e,n,t,u,1,l,1,i,0):e.getToken()===14&&(a=g2(e,n,t,u,16,i,0,0,l,1)),g=1,e.destructible&48&&f(e,50)),e.getToken()===1077936155){k(e,n|8192),g=1;let h=L(e,n,u,1,0,e.tokenStart);a=e.finishNode({type:"AssignmentPattern",left:a,right:h},s)}if(d++,c.push(a),!r(e,n,18)||e.getToken()===16)break}return o&512&&d!==1&&f(e,37,"Setter","one",""),t&&t.scopeError&&V2(t.scopeError),g&&(e.flags|=128),A(e,n,16),c}function c2(e,n,t,u){k(e,n|8192);let o=L(e,(n|33554432)^33554432,t,1,u,e.tokenStart);return A(e,n,20),o}function lu(e,n,t,u,o,i,l){e.flags=(e.flags|128)^128;let c=e.tokenStart;k(e,n|8192|67108864);let d=n&16?q(l2(),1024):void 0;if(n=(n|33554432)^33554432,r(e,n,16))return O2(e,n,d,t,[],u,0,l);let g=0;e.destructible&=-385;let a,s=[],h=0,T=0,C=0,E=e.tokenStart;for(e.assignable=1;e.getToken()!==16;){let{tokenStart:b}=e,D=e.getToken();if(D&143360)d&&n2(e,n,d,e.tokenValue,1,0),(D&537079808)===537079808?T=1:(D&36864)===36864&&(C=1),a=j(e,n,t,o,0,1,1,1,b),e.getToken()===16||e.getToken()===18?e.assignable&2&&(g|=16,T=1):(e.getToken()===1077936155?T=1:g|=16,a=F(e,n,t,a,1,0,b),e.getToken()!==16&&e.getToken()!==18&&(a=I(e,n,t,1,0,b,a)));else if((D&2097152)===2097152)a=D===2162700?z(e,n|67108864,d,t,0,1,0,o,i):H(e,n|67108864,d,t,0,1,0,o,i),g|=e.destructible,T=1,e.assignable=2,e.getToken()!==16&&e.getToken()!==18&&(g&8&&f(e,122),a=F(e,n,t,a,0,0,b),g|=16,e.getToken()!==16&&e.getToken()!==18&&(a=I(e,n,t,0,0,b,a)));else if(D===14){a=g2(e,n,d,t,16,o,i,0,1,0),e.destructible&16&&f(e,74),T=1,h&&(e.getToken()===16||e.getToken()===18)&&s.push(a),g|=8;break}else{if(g|=16,a=L(e,n,t,1,1,b),h&&(e.getToken()===16||e.getToken()===18)&&s.push(a),e.getToken()===18&&(h||(h=1,s=[a])),h){for(;r(e,n|8192,18);)s.push(L(e,n,t,1,1,e.tokenStart));e.assignable=2,a=e.finishNode({type:"SequenceExpression",expressions:s},E)}return A(e,n,16),e.destructible=g,n&32?e.finishNode({type:"ParenthesizedExpression",expression:a},c):a}if(h&&(e.getToken()===16||e.getToken()===18)&&s.push(a),!r(e,n|8192,18))break;if(h||(h=1,s=[a]),e.getToken()===16){g|=8;break}}return h&&(e.assignable=2,a=e.finishNode({type:"SequenceExpression",expressions:s},E)),A(e,n,16),g&16&&g&8&&f(e,151),g|=e.destructible&256?256:0|e.destructible&128?128:0,e.getToken()===10?(g&48&&f(e,49),n&524800&&g&128&&f(e,31),n&262400&&g&256&&f(e,32),T&&(e.flags|=128),C&&(e.flags|=256),O2(e,n,d,t,h?s:[a],u,0,l)):(g&64&&f(e,63),g&8&&f(e,144),e.destructible=(e.destructible|256)^256|g,n&32?e.finishNode({type:"ParenthesizedExpression",expression:a},c):a)}function de(e,n,t){let{tokenStart:u}=e,{tokenValue:o}=e,i=0,l=0;(e.getToken()&537079808)===537079808?i=1:(e.getToken()&36864)===36864&&(l=1);let c=N(e,n);if(e.assignable=1,e.getToken()===10){let d;return n&16&&(d=M2(e,n,o)),i&&(e.flags|=128),l&&(e.flags|=256),E2(e,n,d,t,[c],0,u)}return c}function _2(e,n,t,u,o,i,l,c,d){l||f(e,57),i&&f(e,51),e.flags&=-129;let g=n&16?M2(e,n,u):void 0;return E2(e,n,g,t,[o],c,d)}function O2(e,n,t,u,o,i,l,c){i||f(e,57);for(let d=0;d<o.length;++d)$(e,o[d]);return E2(e,n,t,u,o,l,c)}function E2(e,n,t,u,o,i,l){e.flags&1&&f(e,48),A(e,n|8192,10);let c=271319040;n=(n|c)^c|(i?524288:0);let d=e.getToken()!==2162700,g;if(t&&t.scopeError&&V2(t.scopeError),d)e.flags=(e.flags|512|256|64|4096)^4928,g=L(e,n,u,1,0,e.tokenStart);else{t&&(t=q(t,128));let a=33557504;switch(g=J2(e,(n|a)^a|1048576,t,u,16,void 0,void 0),e.getToken()){case 69271571:(e.flags&1)===0&&f(e,116);break;case 67108877:case 67174409:case 22:f(e,117);case 67174411:(e.flags&1)===0&&f(e,116),e.flags|=1024;break}(e.getToken()&8388608)===8388608&&(e.flags&1)===0&&f(e,30,S[e.getToken()&255]),(e.getToken()&33619968)===33619968&&f(e,125)}return e.assignable=2,e.finishNode({type:"ArrowFunctionExpression",params:o,body:g,async:i===1,expression:d,generator:!1},l)}function nn(e,n,t,u,o,i){A(e,n,67174411),e.flags=(e.flags|128)^128;let l=[];if(r(e,n,16))return l;n=(n|33554432)^33554432;let c=0;for(;e.getToken()!==18;){let d,{tokenStart:g}=e,a=e.getToken();if(a&143360?((n&256)===0&&((a&36864)===36864&&(e.flags|=256),(a&537079808)===537079808&&(e.flags|=512)),d=se(e,n,t,i|1,0)):(a===2162700?d=z(e,n,t,u,1,o,1,i,0):a===69271571?d=H(e,n,t,u,1,o,1,i,0):a===14?d=g2(e,n,t,u,16,i,0,0,o,1):f(e,30,S[a&255]),c=1,e.destructible&48&&f(e,50)),e.getToken()===1077936155){k(e,n|8192),c=1;let s=L(e,n,u,1,o,e.tokenStart);d=e.finishNode({type:"AssignmentPattern",left:d,right:s},g)}if(l.push(d),!r(e,n,18)||e.getToken()===16)break}return c&&(e.flags|=128),t&&(c||n&256)&&t.scopeError&&V2(t.scopeError),A(e,n,16),l}function I2(e,n,t,u,o,i){let l=e.getToken();if(l&67108864){if(l===67108877){k(e,n|67108864),e.assignable=1;let c=oe(e,n,t);return I2(e,n,t,e.finishNode({type:"MemberExpression",object:u,computed:!1,property:c,optional:!1},i),0,i)}else if(l===69271571){k(e,n|8192);let{tokenStart:c}=e,d=V(e,n,t,o,1,c);return A(e,n,20),e.assignable=1,I2(e,n,t,e.finishNode({type:"MemberExpression",object:u,computed:!0,property:d,optional:!1},i),0,i)}else if(l===67174408||l===67174409)return e.assignable=2,I2(e,n,t,e.finishNode({type:"TaggedTemplateExpression",tag:u,quasi:e.getToken()===67174408?fe(e,n|16384,t):le(e,n|16384)},i),0,i)}return u}function fu(e,n,t,u){let{tokenStart:o}=e,i=N(e,n|8192),{tokenStart:l}=e;if(r(e,n,67108877)){if(n&16777216&&e.getToken()===209029)return e.assignable=2,cu(e,n,i,o);f(e,94)}e.assignable=2,(e.getToken()&16842752)===16842752&&f(e,65,S[e.getToken()&255]);let c=j(e,n,t,2,1,0,u,1,l);n=(n|33554432)^33554432,e.getToken()===67108990&&f(e,168);let d=I2(e,n,t,c,u,l);return e.assignable=2,e.finishNode({type:"NewExpression",callee:d,arguments:e.getToken()===67174411?ce(e,n,t,u):[]},o)}function cu(e,n,t,u){let o=N(e,n);return e.finishNode({type:"MetaProperty",meta:t,property:o},u)}function tn(e,n,t,u,o){return e.getToken()===209006&&f(e,31),n&262400&&e.getToken()===241771&&f(e,32),U2(e,n,e.getToken()),(e.getToken()&36864)===36864&&(e.flags|=256),_2(e,n&-268435457|524288,t,e.tokenValue,N(e,n),0,u,1,o)}function ae(e,n,t,u,o,i,l,c,d){k(e,n|8192);let g=n&16?q(l2(),1024):void 0;if(n=(n|33554432)^33554432,r(e,n,16))return e.getToken()===10?(c&1&&f(e,48),O2(e,n,g,t,[],o,1,d)):e.finishNode({type:"CallExpression",callee:u,arguments:[],optional:!1},d);let a=0,s=null,h=0;e.destructible=(e.destructible|256|128)^384;let T=[];for(;e.getToken()!==16;){let{tokenStart:C}=e,E=e.getToken();if(E&143360)g&&n2(e,n,g,e.tokenValue,i,0),(E&537079808)===537079808?e.flags|=512:(E&36864)===36864&&(e.flags|=256),s=j(e,n,t,i,0,1,1,1,C),e.getToken()===16||e.getToken()===18?e.assignable&2&&(a|=16,h=1):(e.getToken()===1077936155?h=1:a|=16,s=F(e,n,t,s,1,0,C),e.getToken()!==16&&e.getToken()!==18&&(s=I(e,n,t,1,0,C,s)));else if(E&2097152)s=E===2162700?z(e,n,g,t,0,1,0,i,l):H(e,n,g,t,0,1,0,i,l),a|=e.destructible,h=1,e.getToken()!==16&&e.getToken()!==18&&(a&8&&f(e,122),s=F(e,n,t,s,0,0,C),a|=16,(e.getToken()&8388608)===8388608&&(s=p(e,n,t,1,d,4,E,s)),r(e,n|8192,22)&&(s=u2(e,n,t,s,d)));else if(E===14)s=g2(e,n,g,t,16,i,l,1,1,0),a|=(e.getToken()===16?0:16)|e.destructible,h=1;else{for(s=L(e,n,t,1,0,C),a=e.assignable,T.push(s);r(e,n|8192,18);)T.push(L(e,n,t,1,0,C));return a|=e.assignable,A(e,n,16),e.destructible=a|16,e.assignable=2,e.finishNode({type:"CallExpression",callee:u,arguments:T,optional:!1},d)}if(T.push(s),!r(e,n|8192,18))break}return A(e,n,16),a|=e.destructible&256?256:0|e.destructible&128?128:0,e.getToken()===10?(a&48&&f(e,27),(e.flags&1||c&1)&&f(e,48),a&128&&f(e,31),n&262400&&a&256&&f(e,32),h&&(e.flags|=128),O2(e,n|524288,g,t,T,o,1,d)):(a&64&&f(e,63),a&8&&f(e,62),e.assignable=2,e.finishNode({type:"CallExpression",callee:u,arguments:T,optional:!1},d))}function du(e,n){let{tokenRaw:t,tokenRegExp:u,tokenValue:o,tokenStart:i}=e;k(e,n),e.assignable=2;let l={type:"Literal",value:o,regex:u};return n&128&&(l.raw=t),e.finishNode(l,i)}function G2(e,n,t,u,o){let i,l;e.leadingDecorators.decorators.length?(e.getToken()===132&&f(e,30,"@"),i=e.leadingDecorators.start,l=[...e.leadingDecorators.decorators],e.leadingDecorators.decorators.length=0):(i=e.tokenStart,l=j2(e,n,u)),n=(n|4194304|256)^4194304,k(e,n);let c=null,d=null,{tokenValue:g}=e;e.getToken()&4096&&e.getToken()!==20565?(je(e,n,e.getToken())&&f(e,118),(e.getToken()&537079808)===537079808&&f(e,119),t&&(n2(e,n,t,g,32,0),o&&o&2&&t2(e,g)),c=N(e,n)):(o&1)===0&&f(e,39,"Class");let a=n;r(e,n|8192,20565)?(d=X(e,n,u,0,0,0),a|=131072):a=(a|131072)^131072;let s=un(e,a,n,t,u,2,8,0);return e.finishNode({type:"ClassDeclaration",id:c,superClass:d,body:s,...n&1?{decorators:l}:null},i)}function au(e,n,t,u,o){let i=null,l=null,c=j2(e,n,t);n=(n|256|4194304)^4194304,k(e,n),e.getToken()&4096&&e.getToken()!==20565&&(je(e,n,e.getToken())&&f(e,118),(e.getToken()&537079808)===537079808&&f(e,119),i=N(e,n));let d=n;r(e,n|8192,20565)?(l=X(e,n,t,0,u,0),d|=131072):d=(d|131072)^131072;let g=un(e,d,n,void 0,t,2,0,u);return e.assignable=2,e.finishNode({type:"ClassExpression",id:i,superClass:l,body:g,...n&1?{decorators:c}:null},o)}function j2(e,n,t){let u=[];if(n&1)for(;e.getToken()===132;)u.push(su(e,n,t));return u}function su(e,n,t){let u=e.tokenStart;k(e,n|8192);let o=j(e,n,t,2,0,1,0,1,u);return o=F(e,n,t,o,0,0,e.tokenStart),e.finishNode({type:"Decorator",expression:o},u)}function un(e,n,t,u,o,i,l,c){let{tokenStart:d}=e,g=n&16?st(o):void 0;A(e,n|8192,2162700);let a=301989888;n=(n|a)^a;let s=e.flags&32;e.flags=(e.flags|32)^32;let h=[],T,C=e.tokenStart;for(;e.getToken()!==1074790415;){let E=0;if(T=j2(e,n,g),E=T.length,E>0&&e.tokenValue==="constructor"&&f(e,109),e.getToken()===1074790415&&f(e,108),r(e,n,1074790417)){E>0&&f(e,120);continue}h.push(on(e,n,u,g,t,i,T,0,c,E>0?C:e.tokenStart))}return A(e,l&8?n|8192:n,1074790415),g&&kt(g),e.flags=e.flags&-33|s,e.finishNode({type:"ClassBody",body:h},d)}function on(e,n,t,u,o,i,l,c,d,g){let a=c?32:0,s=null,h=e.getToken();if(h&176128||h===-2147483528)switch(s=N(e,n),h){case 36970:if(!c&&e.getToken()!==67174411&&(e.getToken()&1048576)!==1048576&&e.getToken()!==1077936155)return on(e,n,t,u,o,i,l,1,d,g);break;case 209005:if(e.getToken()!==67174411&&(e.flags&1)===0){if((e.getToken()&1073741824)===1073741824)return h2(e,n,u,s,a,l,g);a|=16|(te(e,n,8391476)?8:0)}break;case 209008:if(e.getToken()!==67174411){if((e.getToken()&1073741824)===1073741824)return h2(e,n,u,s,a,l,g);a|=256}break;case 209009:if(e.getToken()!==67174411){if((e.getToken()&1073741824)===1073741824)return h2(e,n,u,s,a,l,g);a|=512}break;case 12402:if(e.getToken()!==67174411&&(e.flags&1)===0){if((e.getToken()&1073741824)===1073741824)return h2(e,n,u,s,a,l,g);n&1&&(a|=1024)}break}else if(h===69271571)a|=2,s=c2(e,o,u,d);else if((h&134217728)===134217728)s=v(e,n);else if(h===8391476)a|=8,k(e,n);else if(e.getToken()===130)a|=8192,s=R2(e,n|4096,u,768);else if((e.getToken()&1073741824)===1073741824)a|=128;else{if(c&&h===2162700)return Rt(e,n|4096,t,u,g);h===-2147483527?(s=N(e,n),e.getToken()!==67174411&&f(e,30,S[e.getToken()&255])):f(e,30,S[e.getToken()&255])}if(a&1816&&(e.getToken()&143360||e.getToken()===-2147483528||e.getToken()===-2147483527?s=N(e,n):(e.getToken()&134217728)===134217728?s=v(e,n):e.getToken()===69271571?(a|=2,s=c2(e,n,u,0)):e.getToken()===130?(a|=8192,s=R2(e,n,u,a)):f(e,135)),(a&2)===0&&(e.tokenValue==="constructor"?((e.getToken()&1073741824)===1073741824?f(e,129):(a&32)===0&&e.getToken()===67174411&&(a&920?f(e,53,"accessor"):(n&131072)===0&&(e.flags&32?f(e,54):e.flags|=32)),a|=64):(a&8192)===0&&a&32&&e.tokenValue==="prototype"&&f(e,52)),a&1024||e.getToken()!==67174411&&(a&768)===0)return h2(e,n,u,s,a,l,g);let T=K(e,n|4096,u,a,d,e.tokenStart);return e.finishNode({type:"MethodDefinition",kind:(a&32)===0&&a&64?"constructor":a&256?"get":a&512?"set":"method",static:(a&32)>0,computed:(a&2)>0,key:s,value:T,...n&1?{decorators:l}:null},g)}function R2(e,n,t,u){let{tokenStart:o}=e;k(e,n);let{tokenValue:i}=e;return i==="constructor"&&f(e,128),n&16&&(t||f(e,4,i),u?gt(e,t,i,u):mt(e,t,i)),k(e,n),e.finishNode({type:"PrivateIdentifier",name:i},o)}function h2(e,n,t,u,o,i,l){let c=null;if(o&8&&f(e,0),e.getToken()===1077936155){k(e,n|8192);let{tokenStart:d}=e;e.getToken()===537079927&&f(e,119);let g=2883584|((o&64)===0?4325376:0);n=(n|g)^g|(o&8?262144:0)|(o&16?524288:0)|(o&64?4194304:0)|65536|16777216,c=j(e,n|4096,t,2,0,1,0,1,d),((e.getToken()&1073741824)!==1073741824||(e.getToken()&4194304)===4194304)&&(c=F(e,n|4096,t,c,0,0,d),c=I(e,n|4096,t,0,0,d,c))}return U(e,n),e.finishNode({type:o&1024?"AccessorProperty":"PropertyDefinition",key:u,value:c,static:(o&32)>0,computed:(o&2)>0,...n&1?{decorators:i}:null},l)}function ln(e,n,t,u,o,i){if(e.getToken()&143360||(n&256)===0&&e.getToken()===-2147483527)return se(e,n,t,o,i);(e.getToken()&2097152)!==2097152&&f(e,30,S[e.getToken()&255]);let l=e.getToken()===69271571?H(e,n,t,u,1,0,1,o,i):z(e,n,t,u,1,0,1,o,i);return e.destructible&16&&f(e,50),e.destructible&32&&f(e,50),l}function se(e,n,t,u,o){let i=e.getToken();n&256&&((i&537079808)===537079808?f(e,119):((i&36864)===36864||i===-2147483527)&&f(e,118)),(i&20480)===20480&&f(e,102),i===241771&&(n&262144&&f(e,32),n&512&&f(e,111)),(i&255)===73&&u&24&&f(e,100),i===209006&&(n&524288&&f(e,176),n&512&&f(e,110));let{tokenValue:l,tokenStart:c}=e;return k(e,n),t&&Y(e,n,t,l,u,o),e.finishNode({type:"Identifier",name:l},c)}function X2(e,n,t,u,o){if(u||A(e,n,8456256),e.getToken()===8390721){let d=gu(e,n,o),[g,a]=yu(e,n,t,u);return e.finishNode({type:"JSXFragment",openingFragment:d,children:g,closingFragment:a},o)}e.getToken()===8457014&&f(e,30,S[e.getToken()&255]);let i=null,l=[],c=bu(e,n,t,u,o);if(!c.selfClosing){[l,i]=hu(e,n,t,u);let d=v2(i.name);v2(c.name)!==d&&f(e,155,d)}return e.finishNode({type:"JSXElement",children:l,openingElement:c,closingElement:i},o)}function gu(e,n,t){return T2(e,n),e.finishNode({type:"JSXOpeningFragment"},t)}function mu(e,n,t,u){A(e,n,8457014);let o=cn(e,n);return e.getToken()!==8390721&&f(e,25,S[65]),t?T2(e,n):k(e,n),e.finishNode({type:"JSXClosingElement",name:o},u)}function ku(e,n,t,u){return A(e,n,8457014),e.getToken()!==8390721&&f(e,25,S[65]),t?T2(e,n):k(e,n),e.finishNode({type:"JSXClosingFragment"},u)}function hu(e,n,t,u){let o=[];for(;;){let i=Au(e,n,t,u);if(i.type==="JSXClosingElement")return[o,i];o.push(i)}}function yu(e,n,t,u){let o=[];for(;;){let i=Tu(e,n,t,u);if(i.type==="JSXClosingFragment")return[o,i];o.push(i)}}function Au(e,n,t,u){if(e.getToken()===137)return fn(e,n);if(e.getToken()===2162700)return ge(e,n,t,1,0);if(e.getToken()===8456256){let{tokenStart:o}=e;return k(e,n),e.getToken()===8457014?mu(e,n,u,o):X2(e,n,t,1,o)}f(e,0)}function Tu(e,n,t,u){if(e.getToken()===137)return fn(e,n);if(e.getToken()===2162700)return ge(e,n,t,1,0);if(e.getToken()===8456256){let{tokenStart:o}=e;return k(e,n),e.getToken()===8457014?ku(e,n,u,o):X2(e,n,t,1,o)}f(e,0)}function fn(e,n){let t=e.tokenStart;k(e,n);let u={type:"JSXText",value:e.tokenValue};return n&128&&(u.raw=e.tokenRaw),e.finishNode(u,t)}function bu(e,n,t,u,o){(e.getToken()&143360)!==143360&&(e.getToken()&4096)!==4096&&f(e,0);let i=cn(e,n),l=Cu(e,n,t),c=e.getToken()===8457014;return c&&A(e,n,8457014),e.getToken()!==8390721&&f(e,25,S[65]),u||!c?T2(e,n):k(e,n),e.finishNode({type:"JSXOpeningElement",name:i,attributes:l,selfClosing:c},o)}function cn(e,n){let{tokenStart:t}=e;$2(e);let u=H2(e,n);if(e.getToken()===21)return dn(e,n,u,t);for(;r(e,n,67108877);)$2(e),u=Du(e,n,u,t);return u}function Du(e,n,t,u){let o=H2(e,n);return e.finishNode({type:"JSXMemberExpression",object:t,property:o},u)}function Cu(e,n,t){let u=[];for(;e.getToken()!==8457014&&e.getToken()!==8390721&&e.getToken()!==1048576;)u.push(ru(e,n,t));return u}function Eu(e,n,t){let u=e.tokenStart;k(e,n),A(e,n,14);let o=L(e,n,t,1,0,e.tokenStart);return A(e,n,1074790415),e.finishNode({type:"JSXSpreadAttribute",argument:o},u)}function ru(e,n,t){let{tokenStart:u}=e;if(e.getToken()===2162700)return Eu(e,n,t);$2(e);let o=null,i=H2(e,n);if(e.getToken()===21&&(i=dn(e,n,i,u)),e.getToken()===1077936155)switch(ft(e,n)){case 134283267:o=v(e,n);break;case 8456256:o=X2(e,n,t,0,e.tokenStart);break;case 2162700:o=ge(e,n,t,0,1);break;default:f(e,154)}return e.finishNode({type:"JSXAttribute",value:o,name:i},u)}function dn(e,n,t,u){A(e,n,21);let o=H2(e,n);return e.finishNode({type:"JSXNamespacedName",namespace:t,name:o},u)}function ge(e,n,t,u,o){let{tokenStart:i}=e;k(e,n|8192);let{tokenStart:l}=e;if(e.getToken()===14)return wu(e,n,t,i);let c=null;return e.getToken()===1074790415?(o&&f(e,157),c=Bu(e,{index:e.startIndex,line:e.startLine,column:e.startColumn})):c=L(e,n,t,1,0,l),e.getToken()!==1074790415&&f(e,25,S[15]),u?T2(e,n):k(e,n),e.finishNode({type:"JSXExpressionContainer",expression:c},i)}function wu(e,n,t,u){A(e,n,14);let o=L(e,n,t,1,0,e.tokenStart);return A(e,n,1074790415),e.finishNode({type:"JSXSpreadChild",expression:o},u)}function Bu(e,n){return e.finishNode({type:"JSXEmptyExpression"},n,e.tokenStart)}function H2(e,n){let t=e.tokenStart;e.getToken()&143360||f(e,30,S[e.getToken()&255]);let{tokenValue:u}=e;return k(e,n),e.finishNode({type:"JSXIdentifier",name:u},t)}function an(e,n){return Tt(e,n,0)}function Su(e,n){let t=new SyntaxError(e+" ("+n.loc.start.line+":"+n.loc.start.column+")");return Object.assign(t,n)}var sn=Su;function Fu(e){let n=[];for(let t of e)try{return t()}catch(u){n.push(u)}throw Object.assign(new Error("All combinations failed"),{errors:n})}var gn=Fu;var Nu=(e,n,t)=>{if(!(e&&n==null)){if(n.findLast)return n.findLast(t);for(let u=n.length-1;u>=0;u--){let o=n[u];if(t(o,u,n))return o}}},mn=Nu;var Lu=(e,n,t)=>{if(!(e&&n==null))return Array.isArray(n)||typeof n=="string"?n[t<0?n.length+t:t]:n.at(t)},kn=Lu;var Iu=new Proxy(()=>{},{get:()=>Iu});function M(e){var u,o,i;let n=((u=e.range)==null?void 0:u[0])??e.start,t=(i=((o=e.declaration)==null?void 0:o.decorators)??e.decorators)==null?void 0:i[0];return t?Math.min(M(t),n):n}function O(e){var t;return((t=e.range)==null?void 0:t[1])??e.end}function qu(e){let n=new Set(e);return t=>n.has(t==null?void 0:t.type)}var m2=qu;function Pu(e,n,t){let u=e.originalText.slice(n,t);for(let o of e[Symbol.for("comments")]){let i=M(o);if(i>t)break;let l=O(o);if(l<n)continue;let c=l-i;u=u.slice(0,i-n)+" ".repeat(c)+u.slice(l-n)}return u}var hn=Pu;var vu=m2(["Block","CommentBlock","MultiLine"]),f2=vu;var Ou=m2(["Line","CommentLine","SingleLine","HashbangComment","HTMLOpen","HTMLClose","Hashbang","InterpreterDirective"]),me=Ou;var ke=new WeakMap;function Ru(e){return ke.has(e)||ke.set(e,f2(e)&&e.value[0]==="*"&&/@(?:type|satisfies)\b/u.test(e.value)),ke.get(e)}var yn=Ru;function Vu(e){if(!f2(e))return!1;let n=`*${e.value}*`.split(`
`);return n.length>1&&n.every(t=>t.trimStart()[0]==="*")}var he=new WeakMap;function Mu(e){return he.has(e)||he.set(e,Vu(e)),he.get(e)}var ye=Mu;function Uu(e){if(e.length<2)return;let n;for(let t=e.length-1;t>=0;t--){let u=e[t];if(n&&O(u)===M(n)&&ye(u)&&ye(n)&&(e.splice(t+1,1),u.value+="*//*"+n.value,u.range=[M(u),O(n)]),!me(u)&&!f2(u))throw new TypeError(`Unknown comment type: "${u.type}".`);n=u}}var An=Uu;var r2=null;function w2(e){if(r2!==null&&typeof r2.property){let n=r2;return r2=w2.prototype=null,n}return r2=w2.prototype=e??Object.create(null),new w2}var Ju=10;for(let e=0;e<=Ju;e++)w2();function Ae(e){return w2(e)}function _u(e,n="type"){Ae(e);function t(u){let o=u[n],i=e[o];if(!Array.isArray(i))throw Object.assign(new Error(`Missing visitor keys for '${o}'.`),{node:u});return i}return t}var Tn=_u;var bn={ArrayExpression:["elements"],AssignmentExpression:["left","right"],BinaryExpression:["left","right"],InterpreterDirective:[],Directive:["value"],DirectiveLiteral:[],BlockStatement:["directives","body"],BreakStatement:["label"],CallExpression:["callee","typeParameters","typeArguments","arguments"],CatchClause:["param","body"],ConditionalExpression:["test","consequent","alternate"],ContinueStatement:["label"],DebuggerStatement:[],DoWhileStatement:["body","test"],EmptyStatement:[],ExpressionStatement:["expression"],File:["program"],ForInStatement:["left","right","body"],ForStatement:["init","test","update","body"],FunctionDeclaration:["id","typeParameters","params","predicate","returnType","body"],FunctionExpression:["id","typeParameters","params","returnType","body"],Identifier:["typeAnnotation","decorators"],IfStatement:["test","consequent","alternate"],LabeledStatement:["label","body"],StringLiteral:[],NumericLiteral:[],NullLiteral:[],BooleanLiteral:[],RegExpLiteral:[],LogicalExpression:["left","right"],MemberExpression:["object","property"],NewExpression:["callee","typeParameters","typeArguments","arguments"],Program:["directives","body"],ObjectExpression:["properties"],ObjectMethod:["decorators","key","typeParameters","params","returnType","body"],ObjectProperty:["decorators","key","value"],RestElement:["argument","typeAnnotation","decorators"],ReturnStatement:["argument"],SequenceExpression:["expressions"],ParenthesizedExpression:["expression"],SwitchCase:["test","consequent"],SwitchStatement:["discriminant","cases"],ThisExpression:[],ThrowStatement:["argument"],TryStatement:["block","handler","finalizer"],UnaryExpression:["argument"],UpdateExpression:["argument"],VariableDeclaration:["declarations"],VariableDeclarator:["id","init"],WhileStatement:["test","body"],WithStatement:["object","body"],AssignmentPattern:["left","right","decorators","typeAnnotation"],ArrayPattern:["elements","typeAnnotation","decorators"],ArrowFunctionExpression:["typeParameters","params","predicate","returnType","body"],ClassBody:["body"],ClassExpression:["decorators","id","typeParameters","superClass","superTypeParameters","mixins","implements","body","superTypeArguments"],ClassDeclaration:["decorators","id","typeParameters","superClass","superTypeParameters","mixins","implements","body","superTypeArguments"],ExportAllDeclaration:["source","attributes","exported"],ExportDefaultDeclaration:["declaration"],ExportNamedDeclaration:["declaration","specifiers","source","attributes"],ExportSpecifier:["local","exported"],ForOfStatement:["left","right","body"],ImportDeclaration:["specifiers","source","attributes"],ImportDefaultSpecifier:["local"],ImportNamespaceSpecifier:["local"],ImportSpecifier:["imported","local"],ImportExpression:["source","options"],MetaProperty:["meta","property"],ClassMethod:["decorators","key","typeParameters","params","returnType","body"],ObjectPattern:["decorators","properties","typeAnnotation"],SpreadElement:["argument"],Super:[],TaggedTemplateExpression:["tag","typeParameters","quasi","typeArguments"],TemplateElement:[],TemplateLiteral:["quasis","expressions"],YieldExpression:["argument"],AwaitExpression:["argument"],BigIntLiteral:[],ExportNamespaceSpecifier:["exported"],OptionalMemberExpression:["object","property"],OptionalCallExpression:["callee","typeParameters","typeArguments","arguments"],ClassProperty:["decorators","variance","key","typeAnnotation","value"],ClassAccessorProperty:["decorators","key","typeAnnotation","value"],ClassPrivateProperty:["decorators","variance","key","typeAnnotation","value"],ClassPrivateMethod:["decorators","key","typeParameters","params","returnType","body"],PrivateName:["id"],StaticBlock:["body"],ImportAttribute:["key","value"],AnyTypeAnnotation:[],ArrayTypeAnnotation:["elementType"],BooleanTypeAnnotation:[],BooleanLiteralTypeAnnotation:[],NullLiteralTypeAnnotation:[],ClassImplements:["id","typeParameters"],DeclareClass:["id","typeParameters","extends","mixins","implements","body"],DeclareFunction:["id","predicate"],DeclareInterface:["id","typeParameters","extends","body"],DeclareModule:["id","body"],DeclareModuleExports:["typeAnnotation"],DeclareTypeAlias:["id","typeParameters","right"],DeclareOpaqueType:["id","typeParameters","supertype"],DeclareVariable:["id"],DeclareExportDeclaration:["declaration","specifiers","source","attributes"],DeclareExportAllDeclaration:["source","attributes"],DeclaredPredicate:["value"],ExistsTypeAnnotation:[],FunctionTypeAnnotation:["typeParameters","this","params","rest","returnType"],FunctionTypeParam:["name","typeAnnotation"],GenericTypeAnnotation:["id","typeParameters"],InferredPredicate:[],InterfaceExtends:["id","typeParameters"],InterfaceDeclaration:["id","typeParameters","extends","body"],InterfaceTypeAnnotation:["extends","body"],IntersectionTypeAnnotation:["types"],MixedTypeAnnotation:[],EmptyTypeAnnotation:[],NullableTypeAnnotation:["typeAnnotation"],NumberLiteralTypeAnnotation:[],NumberTypeAnnotation:[],ObjectTypeAnnotation:["properties","indexers","callProperties","internalSlots"],ObjectTypeInternalSlot:["id","value"],ObjectTypeCallProperty:["value"],ObjectTypeIndexer:["variance","id","key","value"],ObjectTypeProperty:["key","value","variance"],ObjectTypeSpreadProperty:["argument"],OpaqueType:["id","typeParameters","supertype","impltype"],QualifiedTypeIdentifier:["qualification","id"],StringLiteralTypeAnnotation:[],StringTypeAnnotation:[],SymbolTypeAnnotation:[],ThisTypeAnnotation:[],TupleTypeAnnotation:["types","elementTypes"],TypeofTypeAnnotation:["argument","typeArguments"],TypeAlias:["id","typeParameters","right"],TypeAnnotation:["typeAnnotation"],TypeCastExpression:["expression","typeAnnotation"],TypeParameter:["bound","default","variance"],TypeParameterDeclaration:["params"],TypeParameterInstantiation:["params"],UnionTypeAnnotation:["types"],Variance:[],VoidTypeAnnotation:[],EnumDeclaration:["id","body"],EnumBooleanBody:["members"],EnumNumberBody:["members"],EnumStringBody:["members"],EnumSymbolBody:["members"],EnumBooleanMember:["id","init"],EnumNumberMember:["id","init"],EnumStringMember:["id","init"],EnumDefaultedMember:["id"],IndexedAccessType:["objectType","indexType"],OptionalIndexedAccessType:["objectType","indexType"],JSXAttribute:["name","value"],JSXClosingElement:["name"],JSXElement:["openingElement","children","closingElement"],JSXEmptyExpression:[],JSXExpressionContainer:["expression"],JSXSpreadChild:["expression"],JSXIdentifier:[],JSXMemberExpression:["object","property"],JSXNamespacedName:["namespace","name"],JSXOpeningElement:["name","typeParameters","typeArguments","attributes"],JSXSpreadAttribute:["argument"],JSXText:[],JSXFragment:["openingFragment","children","closingFragment"],JSXOpeningFragment:[],JSXClosingFragment:[],Noop:[],Placeholder:[],V8IntrinsicIdentifier:[],ArgumentPlaceholder:[],BindExpression:["object","callee"],Decorator:["expression"],DoExpression:["body"],ExportDefaultSpecifier:["exported"],ModuleExpression:["body"],TopicReference:[],PipelineTopicExpression:["expression"],PipelineBareFunction:["callee"],PipelinePrimaryTopicReference:[],TSParameterProperty:["parameter","decorators"],TSDeclareFunction:["id","typeParameters","params","returnType","body"],TSDeclareMethod:["decorators","key","typeParameters","params","returnType"],TSQualifiedName:["left","right"],TSCallSignatureDeclaration:["typeParameters","parameters","typeAnnotation","params","returnType"],TSConstructSignatureDeclaration:["typeParameters","parameters","typeAnnotation","params","returnType"],TSPropertySignature:["key","typeAnnotation"],TSMethodSignature:["key","typeParameters","parameters","typeAnnotation","params","returnType"],TSIndexSignature:["parameters","typeAnnotation"],TSAnyKeyword:[],TSBooleanKeyword:[],TSBigIntKeyword:[],TSIntrinsicKeyword:[],TSNeverKeyword:[],TSNullKeyword:[],TSNumberKeyword:[],TSObjectKeyword:[],TSStringKeyword:[],TSSymbolKeyword:[],TSUndefinedKeyword:[],TSUnknownKeyword:[],TSVoidKeyword:[],TSThisType:[],TSFunctionType:["typeParameters","parameters","typeAnnotation","params","returnType"],TSConstructorType:["typeParameters","parameters","typeAnnotation","params","returnType"],TSTypeReference:["typeName","typeParameters","typeArguments"],TSTypePredicate:["parameterName","typeAnnotation"],TSTypeQuery:["exprName","typeParameters","typeArguments"],TSTypeLiteral:["members"],TSArrayType:["elementType"],TSTupleType:["elementTypes"],TSOptionalType:["typeAnnotation"],TSRestType:["typeAnnotation"],TSNamedTupleMember:["label","elementType"],TSUnionType:["types"],TSIntersectionType:["types"],TSConditionalType:["checkType","extendsType","trueType","falseType"],TSInferType:["typeParameter"],TSParenthesizedType:["typeAnnotation"],TSTypeOperator:["typeAnnotation"],TSIndexedAccessType:["objectType","indexType"],TSMappedType:["nameType","typeAnnotation","key","constraint"],TSTemplateLiteralType:["quasis","types"],TSLiteralType:["literal"],TSExpressionWithTypeArguments:["expression","typeParameters"],TSInterfaceDeclaration:["id","typeParameters","extends","body"],TSInterfaceBody:["body"],TSTypeAliasDeclaration:["id","typeParameters","typeAnnotation"],TSInstantiationExpression:["expression","typeParameters","typeArguments"],TSAsExpression:["expression","typeAnnotation"],TSSatisfiesExpression:["expression","typeAnnotation"],TSTypeAssertion:["typeAnnotation","expression"],TSEnumBody:["members"],TSEnumDeclaration:["id","body"],TSEnumMember:["id","initializer"],TSModuleDeclaration:["id","body"],TSModuleBlock:["body"],TSImportType:["argument","options","qualifier","typeParameters","typeArguments"],TSImportEqualsDeclaration:["id","moduleReference"],TSExternalModuleReference:["expression"],TSNonNullExpression:["expression"],TSExportAssignment:["expression"],TSNamespaceExportDeclaration:["id"],TSTypeAnnotation:["typeAnnotation"],TSTypeParameterInstantiation:["params"],TSTypeParameterDeclaration:["params"],TSTypeParameter:["constraint","default","name"],ChainExpression:["expression"],ExperimentalRestProperty:["argument"],ExperimentalSpreadProperty:["argument"],Literal:[],MethodDefinition:["decorators","key","value"],PrivateIdentifier:[],Property:["key","value"],PropertyDefinition:["decorators","key","typeAnnotation","value","variance"],AccessorProperty:["decorators","key","typeAnnotation","value"],TSAbstractAccessorProperty:["decorators","key","typeAnnotation"],TSAbstractKeyword:[],TSAbstractMethodDefinition:["key","value"],TSAbstractPropertyDefinition:["decorators","key","typeAnnotation"],TSAsyncKeyword:[],TSClassImplements:["expression","typeArguments","typeParameters"],TSDeclareKeyword:[],TSEmptyBodyFunctionExpression:["id","typeParameters","params","returnType"],TSExportKeyword:[],TSInterfaceHeritage:["expression","typeArguments","typeParameters"],TSPrivateKeyword:[],TSProtectedKeyword:[],TSPublicKeyword:[],TSReadonlyKeyword:[],TSStaticKeyword:[],AsConstExpression:["expression"],AsExpression:["expression","typeAnnotation"],BigIntLiteralTypeAnnotation:[],BigIntTypeAnnotation:[],ComponentDeclaration:["id","params","body","typeParameters","rendersType"],ComponentParameter:["name","local"],ComponentTypeAnnotation:["params","rest","typeParameters","rendersType"],ComponentTypeParameter:["name","typeAnnotation"],ConditionalTypeAnnotation:["checkType","extendsType","trueType","falseType"],DeclareComponent:["id","params","rest","typeParameters","rendersType"],DeclareEnum:["id","body"],DeclareHook:["id"],DeclareNamespace:["id","body"],EnumBigIntBody:["members"],EnumBigIntMember:["id","init"],HookDeclaration:["id","params","body","typeParameters","returnType"],HookTypeAnnotation:["params","returnType","rest","typeParameters"],InferTypeAnnotation:["typeParameter"],KeyofTypeAnnotation:["argument"],ObjectTypeMappedTypeProperty:["keyTparam","propType","sourceType","variance"],QualifiedTypeofIdentifier:["qualification","id"],TupleTypeLabeledElement:["label","elementType","variance"],TupleTypeSpreadElement:["label","typeAnnotation"],TypeOperator:["typeAnnotation"],TypePredicate:["parameterName","typeAnnotation","asserts"],NGChainedExpression:["expressions"],NGEmptyExpression:[],NGPipeExpression:["left","right","arguments"],NGMicrosyntax:["body"],NGMicrosyntaxAs:["key","alias"],NGMicrosyntaxExpression:["expression","alias"],NGMicrosyntaxKey:[],NGMicrosyntaxKeyedExpression:["key","expression"],NGMicrosyntaxLet:["key","value"],NGRoot:["node"],JsExpressionRoot:["node"],JsonRoot:["node"],TSJSDocAllType:[],TSJSDocUnknownType:[],TSJSDocNullableType:["typeAnnotation"],TSJSDocNonNullableType:["typeAnnotation"],NeverTypeAnnotation:[],SatisfiesExpression:["expression","typeAnnotation"],UndefinedTypeAnnotation:[],UnknownTypeAnnotation:[]};var ju=Tn(bn),Dn=ju;function Te(e,n){if(!(e!==null&&typeof e=="object"))return e;if(Array.isArray(e)){for(let u=0;u<e.length;u++)e[u]=Te(e[u],n);return e}let t=Dn(e);for(let u=0;u<t.length;u++)e[t[u]]=Te(e[t[u]],n);return n(e)||e}var Cn=Te;var t0=m2(["RegExpLiteral","BigIntLiteral","NumericLiteral","StringLiteral","DirectiveLiteral","Literal","JSXText","TemplateElement","StringLiteralTypeAnnotation","NumberLiteralTypeAnnotation","BigIntLiteralTypeAnnotation"]);function Xu(e,n){let{parser:t,text:u}=n,{comments:o}=e,i=t==="oxc"&&n.oxcAstType==="ts";An(o);let l;e=Cn(e,d=>{switch(d.type){case"ParenthesizedExpression":{let{expression:g}=d,a=M(d);if(g.type==="TypeCastExpression")return g.range=[a,O(d)],g;let s=!1;if(!i){if(!l){l=[];for(let T of o)yn(T)&&l.push(O(T))}let h=mn(!1,l,T=>T<=a);s=h&&u.slice(h,a).trim().length===0}if(!s)return g.extra={...g.extra,parenthesized:!0},g;break}case"LogicalExpression":if(rn(d))return be(d);break;case"TemplateLiteral":if(d.expressions.length!==d.quasis.length-1)throw new Error("Malformed template literal.");break;case"TemplateElement":if(t==="flow"||t==="hermes"||t==="espree"||t==="typescript"||i){let g=M(d)+1,a=O(d)-(d.tail?1:2);d.range=[g,a]}break;case"VariableDeclaration":{let g=kn(!1,d.declarations,-1);g!=null&&g.init&&u[O(g)]!==";"&&(d.range=[M(d),O(g)]);break}case"TSParenthesizedType":return d.typeAnnotation;case"TSTypeParameter":En(d);break;case"TopicReference":e.extra={...e.extra,__isUsingHackPipeline:!0};break;case"TSUnionType":case"TSIntersectionType":if(d.types.length===1)return d.types[0];break;case"TSMappedType":if(!d.constraint&&!d.key){let{name:g,constraint:a}=En(d.typeParameter);d.constraint=a,d.key=g,delete d.typeParameter}break;case"TSEnumDeclaration":if(!d.body){let g=O(d.id),{members:a}=d,s=hn({originalText:u,[Symbol.for("comments")]:o},g,a[0]?M(a[0]):O(d)),h=g+s.indexOf("{");d.body={type:"TSEnumBody",members:a,range:[h,O(d)]},delete d.members}break;case"ImportExpression":t==="hermes"&&d.attributes&&!d.options&&(d.options=d.attributes);break}});let c=e.type==="File"?e.program:e;return c.interpreter&&(o.unshift(c.interpreter),delete c.interpreter),i&&e.hashbang&&(o.unshift(e.hashbang),delete e.hashbang),e.type==="Program"&&(e.range=[0,u.length]),e}function En(e){if(e.type==="TSTypeParameter"&&typeof e.name=="string"){let n=M(e);e.name={type:"Identifier",name:e.name,range:[n,n+e.name.length]}}return e}function rn(e){return e.type==="LogicalExpression"&&e.right.type==="LogicalExpression"&&e.operator===e.right.operator}function be(e){return rn(e)?be({type:"LogicalExpression",operator:e.operator,left:be({type:"LogicalExpression",operator:e.operator,left:e.left,right:e.right.left,range:[M(e.left),O(e.right.left)]}),right:e.right.right,range:[M(e),O(e)]}):e}var wn=Xu;var Hu=/\*\/$/,zu=/^\/\*\*?/,Ku=/^\s*(\/\*\*?(.|\r?\n)*?\*\/)/,$u=/(^|\s+)\/\/([^\n\r]*)/g,Bn=/^(\r?\n)+/,Wu=/(?:^|\r?\n) *(@[^\n\r]*?) *\r?\n *(?![^\n\r@]*\/\/[^]*)([^\s@][^\n\r@]+?) *\r?\n/g,Sn=/(?:^|\r?\n) *@(\S+) *([^\n\r]*)/g,Yu=/(\r?\n|^) *\* ?/g,Qu=[];function Fn(e){let n=e.match(Ku);return n?n[0].trimStart():""}function Nn(e){let n=`
`;e=G(!1,e.replace(zu,"").replace(Hu,""),Yu,"$1");let t="";for(;t!==e;)t=e,e=G(!1,e,Wu,`${n}$1 $2${n}`);e=e.replace(Bn,"").trimEnd();let u=Object.create(null),o=G(!1,e,Sn,"").replace(Bn,"").trimEnd(),i;for(;i=Sn.exec(e);){let l=G(!1,i[2],$u,"");if(typeof u[i[1]]=="string"||Array.isArray(u[i[1]])){let c=u[i[1]];u[i[1]]=[...Qu,...Array.isArray(c)?c:[c],l]}else u[i[1]]=l}return{comments:o,pragmas:u}}var Ln=["noformat","noprettier"],In=["format","prettier"];function Zu(e){if(!e.startsWith("#!"))return"";let n=e.indexOf(`
`);return n===-1?e:e.slice(0,n)}var qn=Zu;function Pn(e){let n=qn(e);n&&(e=e.slice(n.length+1));let t=Fn(e),{pragmas:u,comments:o}=Nn(t);return{shebang:n,text:e,pragmas:u,comments:o}}function vn(e){let{pragmas:n}=Pn(e);return In.some(t=>Object.prototype.hasOwnProperty.call(n,t))}function On(e){let{pragmas:n}=Pn(e);return Ln.some(t=>Object.prototype.hasOwnProperty.call(n,t))}function Gu(e){return e=typeof e=="function"?{parse:e}:e,{astFormat:"estree",hasPragma:vn,hasIgnorePragma:On,locStart:M,locEnd:O,...e}}var Rn=Gu;var z2="module",Vn="script",Mn=[z2,Vn];function Un(e){if(typeof e=="string"){if(e=e.toLowerCase(),/\.(?:mjs|mts)$/iu.test(e))return z2;if(/\.(?:cjs|cts)$/iu.test(e))return Vn}}var xu={next:!0,ranges:!0,webcompat:!0,loc:!1,raw:!0,directives:!0,globalReturn:!0,impliedStrict:!1,preserveParens:!0,lexical:!1,jsx:!0,uniqueKeyInPattern:!1};function pu(e,n){let t=[],u=an(e,{...xu,module:n===z2,onComment:t});return u.comments=t,u}function e1(e){let{description:n,loc:t}=e;return t?sn(n,{loc:{start:{line:t.start.line,column:t.start.column+1},end:{line:t.end.line,column:t.end.column+1}},cause:e}):e}function n1(e,n){let t=Un(n==null?void 0:n.filepath),u=(t?[t]:Mn).map(i=>()=>pu(e,i)),o;try{o=gn(u)}catch({errors:[i]}){throw e1(i)}return wn(o,{parser:"meriyah",text:e})}var t1=Rn(n1);return Hn(u1);});