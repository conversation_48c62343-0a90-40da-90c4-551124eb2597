{"name": "fumadocs-payloadcms", "type": "module", "version": "0.0.0", "private": true, "scripts": {"ri": "set -ex; git clean -fdX -e '!.env*'; pnpm i", "clean": "rm -rf sqlite.db; rm -rf media", "build": "next build", "dev": "next dev", "start": "next build; next start", "postinstall": "fumadocs-mdx"}, "dependencies": {"@libsql/client": "^0.15.10", "@payloadcms/db-sqlite": "^3.48.0", "@payloadcms/next": "^3.48.0", "@payloadcms/payload-cloud": "^3.48.0", "@payloadcms/richtext-lexical": "^3.48.0", "@payloadcms/storage-s3": "^3.48.0", "@payloadcms/storage-vercel-blob": "^3.48.0", "fumadocs-core": "15.6.5", "fumadocs-mdx": "11.7.0", "fumadocs-ui": "15.6.5", "graphql": "^16.11.0", "libsql": "^0.5.16", "next": "15.4.3", "payload": "^3.48.0", "react": "^19.1.0", "react-dom": "^19.1.0", "sharp": "^0.34.3"}, "devDependencies": {"@tailwindcss/postcss": "^4.1.11", "@types/mdx": "^2.0.13", "@types/node": "24.1.0", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "eslint": "^8", "eslint-config-next": "15.4.3", "postcss": "^8.5.6", "tailwindcss": "^4.1.11", "typescript": "^5.8.3"}, "optionalDependencies": {"@libsql/darwin-arm64": "0.5.16", "@libsql/linux-arm64-gnu": "0.5.16", "@libsql/linux-x64-gnu": "0.5.16"}}